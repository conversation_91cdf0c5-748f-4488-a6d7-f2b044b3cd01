use serde::{Deserialize, Serialize};
use tauri::{command, Emitter};
use reqwest;

// ===== LiveKit SDK Imports & Globals =====
use std::sync::{Arc, Mutex};
use std::sync::atomic::{AtomicU32, Ordering};
use livekit::prelude::*;
use livekit::webrtc::audio_stream::native::NativeAudioStream;
use livekit::webrtc::prelude::AudioFrame;
use livekit::webrtc::audio_source::{AudioSourceOptions, RtcAudioSource};
use livekit::webrtc::audio_source::native::NativeAudioSource;
use livekit::options::TrackPublishOptions;
use futures::StreamExt;
use log::{info, error, warn};
use cpal::traits::{DeviceTrait, HostTrait, StreamTrait};
use cpal::{StreamConfig, SampleFormat, SampleRate};
use std::sync::mpsc;
use std::mem;

// Global room state
static ROOM_STATE: Mutex<Option<Arc<Room>>> = Mutex::new(None);
static ROOM_CONNECTED: Mutex<bool> = Mutex::new(false);

// Global audio publishing state
static AUDIO_PUBLISHING: Mutex<bool> = Mutex::new(false);
static AUDIO_SOURCE: Mutex<Option<Arc<NativeAudioSource>>> = Mutex::new(None);
static AUDIO_TRACK: Mutex<Option<LocalAudioTrack>> = Mutex::new(None);
static AUDIO_PLAYBACK_BUFFER: Mutex<std::collections::VecDeque<f32>> = Mutex::new(std::collections::VecDeque::new());
// Audio stream control - track if stream is active for cleanup purposes
static AUDIO_STREAM_ACTIVE: Mutex<bool> = Mutex::new(false);
// Audio stream cleanup channel - to signal stream thread to shutdown  
static AUDIO_STREAM_SHUTDOWN: Mutex<Option<mpsc::Sender<()>>> = Mutex::new(None);
// Remote audio playback control - track if remote audio is muted
static REMOTE_AUDIO_MUTED: Mutex<bool> = Mutex::new(false);

// Global app handle for events
static APP_HANDLE: Mutex<Option<tauri::AppHandle>> = Mutex::new(None);

#[derive(Debug, Serialize, Deserialize)]
pub struct ConnectionStatus {
    pub connected: bool,
    pub room_name: String,
    pub participant_count: usize,
    pub audio_tracks: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AudioPublishStatus {
    pub is_publishing: bool,
    pub error: Option<String>,
    pub device_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LiveKitTokenRequest {
    pub event: String,
    pub live_session_id: String,
    pub subject: String,
    pub user_id: u32,
    pub username: String,
    pub can_publish: String,
    pub metadata: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LiveKitTokenResponse {
    pub token: String,
}

/// Request LiveKit token via Tauri command
#[command]
pub async fn get_livekit_token(
    name: String,
    user_id: u32,
    room_name: String,
    event_type: String,
    metadata: Option<String>,
) -> Result<String, String> {
    println!("🔧 [LIVEKIT_TAURI] Making token request with params:");
    println!("  - name: {}", name);
    println!("  - user_id: {}", user_id);
    println!("  - room_name: {}", room_name);
    println!("  - event_type: {}", event_type);
    println!("  - metadata: {:?}", metadata);

    // Use provided user_id directly (no random generation)
    let username = format!("{}{}", name, user_id);
    
    // Use provided metadata or empty string if None
    let final_metadata = metadata.unwrap_or_else(|| "".to_string());

    // Prepare request payload
    let payload = LiveKitTokenRequest {
        event: event_type,
        live_session_id: room_name,
        subject: "null".to_string(),
        user_id,
        username,
        can_publish: "true".to_string(),
        metadata: final_metadata,
    };

    println!("🌐 [LIVEKIT_TAURI] Sending request to token server...");

    // Make HTTP request to LiveKit token server
    let client = reqwest::Client::new();
    let response = client
        .post("https://habibmeet.nwhco.ir/test/sfu/token")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await
        .map_err(|e| {
            let error_msg = format!("Failed to send request: {}", e);
            println!("❌ [LIVEKIT_TAURI] {}", error_msg);
            error_msg
        })?;

    if !response.status().is_success() {
        let error_msg = format!("HTTP error! status: {}", response.status());
        println!("❌ [LIVEKIT_TAURI] {}", error_msg);
        return Err(error_msg);
    }

    let token_response: LiveKitTokenResponse = response
        .json()
        .await
        .map_err(|e| {
            let error_msg = format!("Failed to parse response: {}", e);
            println!("❌ [LIVEKIT_TAURI] {}", error_msg);
            error_msg
        })?;

    println!("✅ [LIVEKIT_TAURI] Token received successfully");
    println!("🔧 [LIVEKIT_TAURI] Token length: {}", token_response.token.len());

    Ok(token_response.token)
}

// ===== LiveKit Room Connection & Audio ==== 

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ParticipantInfo {


    
    pub sid: String,
    pub identity: String,
    pub name: String,
    pub metadata: String,
    pub is_local: bool,
    pub audio_tracks: Vec<String>,
}

// Global participants list
static ROOM_PARTICIPANTS: Mutex<Vec<ParticipantInfo>> = Mutex::new(Vec::new());

#[command]
pub async fn connect_to_room(token: String, room_name: String) -> Result<ConnectionStatus, String> {
    info!("🔗 [LIVEKIT_TAURI] Attempting to connect to room: {}", room_name);

    // Auto-disconnect from existing room if connected
    let is_already_connected = {
        let connected = ROOM_CONNECTED.lock().map_err(|e| format!("Failed to lock room state: {}", e))?;
        *connected
    };
    
    if is_already_connected {
        info!("⚠️ [LIVEKIT_TAURI] Already connected to a room, disconnecting first...");
        if let Err(e) = disconnect_from_room().await {
            warn!("⚠️ [LIVEKIT_TAURI] Failed to disconnect from existing room: {}", e);
        }
        info!("✅ [LIVEKIT_TAURI] Previous room disconnected, proceeding with new connection...");
    }

    if token.trim().is_empty() {
        return Err("Token cannot be empty".to_string());
    }
    if room_name.trim().is_empty() {
        return Err("Room name cannot be empty".to_string());
    }

    let url = "wss://livekit.newhorizonco.uk";
    info!("🌐 [LIVEKIT_TAURI] Connecting to LiveKit server: {}", url);

    let (room, mut room_events) = Room::connect(url, &token, RoomOptions::default())
        .await
        .map_err(|e| format!("Failed to connect to room: {}", e))?;

    info!("✅ [LIVEKIT_TAURI] Connected to room: {}", room.name());

    // Store room and update connection state
    {
        let mut state = ROOM_STATE.lock().map_err(|e| format!("Failed to lock room state: {}", e))?;
        *state = Some(Arc::new(room));
        let mut connected = ROOM_CONNECTED.lock().unwrap();
        *connected = true;
    }

    // Spawn task to handle room events
    tauri::async_runtime::spawn(async move {
        info!("🎧 [LIVEKIT_TAURI] Starting to listen for room events...");

        while let Some(event) = room_events.recv().await {
            match event {
                RoomEvent::TrackSubscribed { track, publication, participant } => {
                    info!("🎵 [LIVEKIT_TAURI] Track subscribed: {:?} from participant: {:?}",
                          publication.sid(), participant.identity());

                    match track {
                        RemoteTrack::Audio(audio_track) => {
                            info!("🔊 [LIVEKIT_TAURI] Audio track received, starting playback...");
                            let rtc_track = audio_track.rtc_track();
                            let mut audio_stream = NativeAudioStream::new(rtc_track, 48000, 2);

                            tauri::async_runtime::spawn(async move {
                                // Start audio playback system
                                if let Err(e) = start_audio_playback() {
                                    warn!("⚠️ [LIVEKIT_TAURI] Failed to start audio playback: {}", e);
                                }

                                // Process incoming audio frames
                                info!("🔊 [LIVEKIT_TAURI] Starting to process incoming audio frames...");
                                let mut frame_count = 0u32;
                                let mut audio_frames_with_data = 0u32;

                                while let Some(audio_frame) = audio_stream.next().await {
                                    frame_count += 1;
                                    let processed_samples = process_audio_frame(&audio_frame);

                                    // Check if frame has actual audio data
                                    let has_audio_data = processed_samples.iter().any(|&sample| sample.abs() > 0.001);
                                    if has_audio_data {
                                        audio_frames_with_data += 1;
                                    }

                                    // Add processed samples to playback buffer
                                    if let Ok(mut buffer) = AUDIO_PLAYBACK_BUFFER.lock() {
                                        let samples_added = processed_samples.len();
                                        for sample in processed_samples {
                                            buffer.push_back(sample);
                                            if buffer.len() > 96000 { // ~2 seconds at 48kHz stereo
                                                buffer.pop_front();
                                            }
                                        }

                                        // Log progress every 100 frames for debugging
                                        if frame_count % 100 == 0 {
                                            info!("📊 [LIVEKIT_TAURI] Audio frames processed: {}, with data: {}, samples added: {}, buffer size: {}",
                                                  frame_count, audio_frames_with_data, samples_added, buffer.len());
                                        }
                                    }
                                }
                                info!("🔇 [LIVEKIT_TAURI] Audio stream ended after {} frames ({} with audio data)",
                                      frame_count, audio_frames_with_data);
                            });
                        },
                        RemoteTrack::Video(video_track) => {
                            info!("📹 [LIVEKIT_TAURI] Video track received: {:?}", video_track.sid());
                        },
                    }
                },
                RoomEvent::ParticipantConnected(participant) => {
                    info!("🟢 [LIVEKIT_TAURI] Participant CONNECTED: identity='{}', sid='{}', name='{}', metadata='{}'", 
                        participant.identity(), 
                        participant.sid(),
                        participant.name(),
                        participant.metadata()
                    );
                    update_participants_list().await;
                    notify_participant_change("connected", &participant).await;
                },
                RoomEvent::ParticipantDisconnected(participant) => {
                    info!("🔴 [LIVEKIT_TAURI] Participant DISCONNECTED: identity='{}', sid='{}', name='{}', metadata='{}'", 
                        participant.identity(), 
                        participant.sid(),
                        participant.name(),
                        participant.metadata()
                    );
                    update_participants_list().await;
                    notify_participant_change("disconnected", &participant).await;
                },
                RoomEvent::Disconnected { reason } => {
                    warn!("🔌 [LIVEKIT_TAURI] Room disconnected: {:?}", reason);
                    // Update connection state
                    if let Ok(mut connected) = ROOM_CONNECTED.lock() {
                        *connected = false;
                    }
                    if let Ok(mut state) = ROOM_STATE.lock() {
                        *state = None;
                    }
                    break;
                },
                _ => {
                    info!("📡 [LIVEKIT_TAURI] Room event: {:?}", event);
                }
            }
        }

        info!("🏁 [LIVEKIT_TAURI] Room event loop ended");
    });

    // Get initial participants list
    update_participants_list().await;

    // Auto-request microphone permission and start audio publishing (like working example)
    info!("🎤 [LIVEKIT_TAURI] Auto-requesting microphone permission and starting audio publishing...");
    
    // First, request microphone permission
    match request_microphone_permission().await {
        Ok(true) => {
            info!("✅ [LIVEKIT_TAURI] Microphone permission granted, starting audio publishing...");
            
            // Start audio publishing immediately
            match start_audio_publishing().await {
                Ok(_) => {
                    info!("✅ [LIVEKIT_TAURI] Audio publishing started automatically after room connection");
                },
                Err(e) => {
                    warn!("⚠️ [LIVEKIT_TAURI] Failed to auto-start audio publishing: {}", e);
                }
            }
        },
        Ok(false) => {
            warn!("⚠️ [LIVEKIT_TAURI] Microphone permission denied by user");
        },
        Err(e) => {
            warn!("⚠️ [LIVEKIT_TAURI] Failed to request microphone permission: {}", e);
        }
    }

    Ok(ConnectionStatus {
        connected: true,
        room_name,
        participant_count: 1,
        audio_tracks: vec![],
    })
}

#[command]
pub async fn disconnect_from_room() -> Result<(), String> {
    info!("🔌 [LIVEKIT_TAURI] Disconnecting from room and resetting all state...");
    
    // Stop audio publishing first
    let should_stop_audio = if let Ok(publishing) = AUDIO_PUBLISHING.lock() {
        *publishing
    } else {
        false
    };
    
    if should_stop_audio {
        if let Err(e) = stop_audio_publishing().await {
            warn!("⚠️ [LIVEKIT_TAURI] Failed to stop audio publishing during disconnect: {}", e);
        }
    }

    // Stop audio playback - CRITICAL FIX for receiving audio!
    if let Err(e) = stop_audio_playback() {
        warn!("⚠️ [LIVEKIT_TAURI] Failed to stop audio playback during disconnect: {}", e);
    }
    
    // Close room connection
    let room_opt = {
        let mut state = ROOM_STATE.lock().map_err(|e| format!("Failed to lock room state: {}", e))?;
        state.take()
    };

    if let Some(room) = room_opt {
        info!("🏠 [LIVEKIT_TAURI] Closing room connection...");
        room.close().await.map_err(|e| format!("Failed to close room: {}", e))?;
        info!("✅ [LIVEKIT_TAURI] Room connection closed");
    } else {
        info!("📝 [LIVEKIT_TAURI] No room connection to close");
    }

    // Reset all global states
    {
        let mut connected = ROOM_CONNECTED.lock().map_err(|e| format!("Failed to lock room connected: {}", e))?;
        *connected = false;
        info!("🔄 [LIVEKIT_TAURI] Room connected state reset");
    }
    
    {
        let mut participants = ROOM_PARTICIPANTS.lock().map_err(|e| format!("Failed to lock participants: {}", e))?;
        participants.clear();
        info!("🔄 [LIVEKIT_TAURI] Participants list cleared");
    }
    
    {
        let mut audio_publishing = AUDIO_PUBLISHING.lock().map_err(|e| format!("Failed to lock audio publishing: {}", e))?;
        *audio_publishing = false;
        info!("🔄 [LIVEKIT_TAURI] Audio publishing state reset");
    }
    
    {
        let mut audio_source = AUDIO_SOURCE.lock().map_err(|e| format!("Failed to lock audio source: {}", e))?;
        if let Some(source) = audio_source.take() {
            info!("🛑 [LIVEKIT_TAURI] Dropping audio source during disconnect - should release microphone");
            drop(source); // Explicitly drop the audio source
        }
        info!("🔄 [LIVEKIT_TAURI] Audio source reset");
    }
    
    {
        let mut audio_track = AUDIO_TRACK.lock().map_err(|e| format!("Failed to lock audio track: {}", e))?;
        *audio_track = None;
        info!("🔄 [LIVEKIT_TAURI] Audio track reset");
    }

    // The audio stream cleanup happens automatically when the audio source is dropped above
    // CPAL will release the microphone when the audio source is no longer referenced
    info!("✅ [LIVEKIT_TAURI] Audio source dropped during disconnect - microphone should be released automatically");
    


    info!("✅ [LIVEKIT_TAURI] Complete disconnect and state reset finished");
    Ok(())
}

// Force cleanup microphone stream specifically
#[command]
pub async fn cleanup_microphone_stream() -> Result<(), String> {
    info!("🎤 [LIVEKIT_TAURI] Cleaning up microphone stream...");

    // Since cpal::Stream cannot be properly cleaned up without process exit,
    // we need to take more drastic action to release the microphone
    info!("🛑 [LIVEKIT_TAURI] Stream cleanup requested - forcing process cleanup to release microphone");
    
    // Clear the shutdown sender to indicate cleanup was attempted
    {
        let mut shutdown_sender = AUDIO_STREAM_SHUTDOWN.lock().map_err(|e| format!("Failed to lock audio stream shutdown: {}", e))?;
        *shutdown_sender = None;
    }
    
    // The stream will only be properly released when the process exits
    // This is a limitation of cpal::Stream not being Send/Sync
    info!("⚠️ [LIVEKIT_TAURI] Stream will be released when process exits due to cpal limitations");
    
    // Try to find and terminate only audio-related threads for this specific process
    info!("🔧 [LIVEKIT_TAURI] Attempting safe audio device release...");
    
    // Get current process ID for targeted cleanup
    let current_pid = std::process::id();
    info!("� [LIVEKIT_TAURI] Current process ID: {}", current_pid);
    
    // Try to identify audio device usage by this specific process
    std::thread::spawn(move || {
        std::thread::sleep(std::time::Duration::from_millis(500));
        
        // Use lsof to find audio device usage by this specific process
        if let Ok(output) = std::process::Command::new("lsof")
            .arg("+D")
            .arg("/dev/snd")
            .arg("-p")
            .arg(&current_pid.to_string())
            .output()
        {
            let output_str = String::from_utf8_lossy(&output.stdout);
            if !output_str.is_empty() {
                info!("🎤 [LIVEKIT_TAURI] Audio device usage detected: {}", output_str);
            } else {
                info!("✅ [LIVEKIT_TAURI] No audio device usage found for this process");
            }
        } else {
            warn!("⚠️ [LIVEKIT_TAURI] Could not check audio device usage (lsof not available)");
        }
    });
    
    info!("📝 [LIVEKIT_TAURI] Safe audio device release initiated");

    // Clear audio source - this should trigger automatic stream cleanup
    {
        let mut audio_source_state = AUDIO_SOURCE.lock().map_err(|e| format!("Failed to lock audio source: {}", e))?;
        if let Some(source) = audio_source_state.take() {
            info!("🛑 [LIVEKIT_TAURI] Dropping audio source for cleanup - should release microphone");
            drop(source); // Explicitly drop the audio source
        }
    }

    // Clear audio track
    {
        let mut audio_track_state = AUDIO_TRACK.lock().map_err(|e| format!("Failed to lock audio track: {}", e))?;
        if audio_track_state.is_some() {
            *audio_track_state = None;
            info!("🛑 [LIVEKIT_TAURI] Audio track cleared");
        }
    }

    // Mark audio stream as inactive
    {
        let mut audio_stream_active_state = AUDIO_STREAM_ACTIVE.lock().map_err(|e| format!("Failed to lock audio stream active: {}", e))?;
        *audio_stream_active_state = false;
    }

    // Reset audio publishing state
    {
        let mut audio_publishing = AUDIO_PUBLISHING.lock().map_err(|e| format!("Failed to lock audio publishing: {}", e))?;
        *audio_publishing = false;
    }

    // Give more time for stream thread to terminate and microphone to be released
    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;

    info!("✅ [LIVEKIT_TAURI] Microphone stream cleanup completed successfully");
    Ok(())
}

// Force cleanup for emergency situations (window closing, app shutdown, etc.)
#[command]
pub async fn emergency_livekit_cleanup() -> Result<(), String> {
    warn!("🚨 [LIVEKIT_TAURI] Emergency cleanup requested - force disconnecting...");
    
    if let Err(e) = disconnect_from_room().await {
        error!("❌ [LIVEKIT_TAURI] Emergency disconnect failed: {}", e);
        
        // Even if disconnect fails, force reset all states
        if let Ok(mut connected) = ROOM_CONNECTED.lock() {
            *connected = false;
        }
        if let Ok(mut participants) = ROOM_PARTICIPANTS.lock() {
            participants.clear();
        }
        if let Ok(mut audio_publishing) = AUDIO_PUBLISHING.lock() {
            *audio_publishing = false;
        }
        if let Ok(mut audio_source) = AUDIO_SOURCE.lock() {
            *audio_source = None;
        }
        if let Ok(mut audio_track) = AUDIO_TRACK.lock() {
            *audio_track = None;
        }
        if let Ok(mut audio_stream_active) = AUDIO_STREAM_ACTIVE.lock() {
            *audio_stream_active = false;
        }

        // Force clear shutdown sender in emergency cleanup
        if let Ok(mut shutdown_sender) = AUDIO_STREAM_SHUTDOWN.lock() {
            *shutdown_sender = None;
            warn!("🚨 [LIVEKIT_TAURI] Emergency: Stream cleanup attempted - will be fully released on process exit");
        }

        // Force clear audio source in emergency cleanup
        if let Ok(mut audio_source_state) = AUDIO_SOURCE.lock() {
            if audio_source_state.is_some() {
                *audio_source_state = None;
                warn!("🚨 [LIVEKIT_TAURI] Emergency audio source cleared - should release microphone");
            }
        }

        if let Ok(mut audio_track_state) = AUDIO_TRACK.lock() {
            if audio_track_state.is_some() {
                *audio_track_state = None;
                warn!("🚨 [LIVEKIT_TAURI] Emergency audio track cleared");
            }
        }

        warn!("✅ [LIVEKIT_TAURI] Emergency audio cleanup completed - microphone should be released");

        if let Ok(mut room_state) = ROOM_STATE.lock() {
            *room_state = None;
        }
        
        warn!("⚠️ [LIVEKIT_TAURI] Force reset all states after emergency cleanup failure");
    }
    
    info!("✅ [LIVEKIT_TAURI] Emergency cleanup completed");
    Ok(())
}

#[command]
pub async fn get_connection_status() -> Result<Option<ConnectionStatus>, String> {
    info!("🔍 [LIVEKIT_TAURI] Getting connection status...");
    
    let connected = ROOM_CONNECTED.lock().map_err(|e| format!("Failed to lock room state: {}", e))?;
    
    if *connected {
        // Get detailed room information
        let (room_name, participant_count) = if let Ok(room_state) = ROOM_STATE.lock() {
            if let Some(room) = room_state.as_ref() {
                let room_name = room.name().to_string();
                let local_count = 1; // Always 1 for local participant
                let remote_count = room.remote_participants().len();
                let total_count = local_count + remote_count;
                
                info!("📊 [LIVEKIT_TAURI] Room '{}' has {} participants (1 local + {} remote)", 
                      room_name, total_count, remote_count);
                
                (room_name, total_count)
            } else {
                ("unknown".to_string(), 0)
            }
        } else {
            ("unknown".to_string(), 0)
        };

        let status = ConnectionStatus { 
            connected: true, 
            room_name, 
            participant_count, 
            audio_tracks: vec![] 
        };
        
        info!("✅ [LIVEKIT_TAURI] Connection status: {:?}", status);
        Ok(Some(status))
    } else {
        info!("❌ [LIVEKIT_TAURI] Not connected to any room");
        Ok(None)
    }
}

// Helper function to update participants list
async fn update_participants_list() {
    info!("🔄 [LIVEKIT_TAURI] Updating participants list...");
    
    if let Ok(room_state) = ROOM_STATE.lock() {
        if let Some(room) = room_state.as_ref() {
            let mut participants_list = Vec::new();
            
            // Add local participant
            let local_participant = room.local_participant();
            let local_metadata = local_participant.metadata();
            let local_info = ParticipantInfo {
                sid: local_participant.sid().to_string(),
                identity: local_participant.identity().to_string(),
                name: local_participant.name(),
                metadata: local_metadata.clone(),
                is_local: true,
                audio_tracks: Vec::new(),
            };
            
            info!("👤 [LIVEKIT_TAURI] Local participant: {:?}", local_info);
            participants_list.push(local_info);

            // Add remote participants
            let remote_count = room.remote_participants().len();
            info!("🌐 [LIVEKIT_TAURI] Found {} remote participants", remote_count);
            
            for remote_participant in room.remote_participants().values() {
                let metadata = remote_participant.metadata();
                let remote_info = ParticipantInfo {
                    sid: remote_participant.sid().to_string(),
                    identity: remote_participant.identity().to_string(),
                    name: remote_participant.name(),
                    metadata: metadata.clone(),
                    is_local: false,
                    audio_tracks: Vec::new(),
                };
                
                info!("👥 [LIVEKIT_TAURI] Remote participant: {:?}", remote_info);
                participants_list.push(remote_info);
            }

            // Update global participants list
            if let Ok(mut participants) = ROOM_PARTICIPANTS.lock() {
                let old_count = participants.len();
                *participants = participants_list;
                info!("📊 [LIVEKIT_TAURI] Participants list updated: {} → {} participants", old_count, participants.len());
            }
        } else {
            warn!("⚠️ [LIVEKIT_TAURI] No room available for updating participants");
        }
    } else {
        error!("❌ [LIVEKIT_TAURI] Failed to lock room state for updating participants");
    }
}

// Request microphone permission 
#[command]
pub async fn request_microphone_permission() -> Result<bool, String> {
    info!("🎤 [LIVEKIT_TAURI] Requesting microphone permission...");

    // Step 1: Request system-level microphone permission on Linux
    #[cfg(target_os = "linux")]
    {
        use std::path::Path;

        // Check if audio devices are accessible
        if !Path::new("/dev/snd").exists() {
            return Err("❌ Audio devices not accessible. Please check system audio configuration.".to_string());
        }

        // Try to request microphone permission using zenity (if available)
        let zenity_check = std::process::Command::new("which")
            .arg("zenity")
            .output();

        if zenity_check.is_ok() && zenity_check.unwrap().status.success() {
            info!("🔍 [LIVEKIT_TAURI] Requesting microphone permission via system dialog...");

            let permission_result = std::process::Command::new("zenity")
                .arg("--question")
                .arg("--title=Microphone Permission")
                .arg("--text=This application needs access to your microphone for audio communication. Allow microphone access?")
                .arg("--width=400")
                .output();

            match permission_result {
                Ok(output) if output.status.success() => {
                    info!("✅ [LIVEKIT_TAURI] User granted microphone permission");
                },
                _ => {
                    return Err("❌ Microphone permission denied by user.".to_string());
                }
            }
        } else {
            info!("⚠️ [LIVEKIT_TAURI] No GUI dialog available, proceeding with direct access test...");
        }
    }

    // Step 2: Test CPAL host and device access
    info!("🔍 [LIVEKIT_TAURI] Testing CPAL audio host access...");
    
    let host = cpal::default_host();
    info!("✅ [LIVEKIT_TAURI] CPAL host initialized: {}", host.id().name());

    // Test default input device
    let default_device = host.default_input_device()
        .ok_or_else(|| "❌ No default input device available.".to_string())?;

    let device_name = default_device.name().unwrap_or_else(|_| "Unknown Device".to_string());
    info!("🎤 [LIVEKIT_TAURI] Default input device: {}", device_name);

    // Test device configuration access
    match default_device.supported_input_configs() {
        Ok(configs) => {
            let config_count = configs.count();
            info!("✅ [LIVEKIT_TAURI] Device supports {} input configurations", config_count);

            if config_count == 0 {
                return Err("❌ Default input device has no supported configurations.".to_string());
            }
        },
        Err(e) => {
            return Err(format!("❌ Cannot access device configurations. This usually means microphone permission is denied. Error: {}", e));
        }
    }

    // Test basic device access by getting default config
    match default_device.default_input_config() {
        Ok(config) => {
            info!("✅ [LIVEKIT_TAURI] Default config: {} channels, {} Hz, {:?}",
                config.channels(),
                config.sample_rate().0,
                config.sample_format()
            );
        },
        Err(e) => {
            return Err(format!("❌ Cannot get default input config: {}", e));
        }
    }

    // Step 3: Test microphone access by attempting to create a stream
    info!("🔍 [LIVEKIT_TAURI] Testing microphone stream creation...");

    let test_config = default_device.default_input_config()
        .map_err(|e| format!("Failed to get default config: {}", e))?;

    let config = cpal::StreamConfig {
        channels: test_config.channels(),
        sample_rate: test_config.sample_rate(),
        buffer_size: cpal::BufferSize::Default,
    };

    // Test stream creation (this will trigger permission request on some systems)
    match default_device.build_input_stream(
        &config,
        |_data: &[f32], _: &cpal::InputCallbackInfo| {
            // Test callback - just to verify stream can be created
        },
        |err| {
            error!("❌ [LIVEKIT_TAURI] Test stream error: {}", err);
        },
        None,
    ) {
        Ok(stream) => {
            info!("✅ [LIVEKIT_TAURI] Test stream created successfully");
            // Try to start the stream briefly
            if let Err(e) = stream.play() {
                warn!("⚠️ [LIVEKIT_TAURI] Could not start test stream: {}", e);
            } else {
                info!("✅ [LIVEKIT_TAURI] Test stream started successfully");
            }
            // Stream will be dropped automatically here
        },
        Err(e) => {
            return Err(format!("❌ Cannot create microphone stream. This usually means microphone permission is denied or device is busy. Error: {}", e));
        }
    }

    info!("✅ [LIVEKIT_TAURI] Microphone permission and access test completed successfully");
    Ok(true)
}

// Get participants list
#[command]
pub async fn get_room_participants() -> Result<Vec<ParticipantInfo>, String> {
    info!("🔍 [LIVEKIT_TAURI] Getting room participants list...");
    
    let participants = ROOM_PARTICIPANTS.lock()
        .map_err(|e| format!("Failed to lock participants list: {}", e))?;
    
    let count = participants.len();
    info!("📋 [LIVEKIT_TAURI] Returning {} participants to React", count);
    
    // Log each participant for debugging
    for (i, participant) in participants.iter().enumerate() {
        info!("  {} [{}] {} ({}): sid={}, metadata='{}'", 
              if participant.is_local { "👤" } else { "👥" },
              i + 1,
              participant.name,
              participant.identity,
              participant.sid,
              participant.metadata
        );
    }
    
    Ok(participants.clone())
}

// Audio capture and publishing functions
#[command]
pub async fn start_audio_publishing() -> Result<AudioPublishStatus, String> {
    info!("🎤 [LIVEKIT_TAURI] Starting audio publishing...");

    // Check if already publishing
    {
        let publishing = AUDIO_PUBLISHING.lock().map_err(|e| format!("Failed to lock audio state: {}", e))?;
        if *publishing {
            return Ok(AudioPublishStatus {
                is_publishing: true,
                error: None,
                device_name: Some("Already publishing".to_string()),
            });
        }
    }

    // Check if connected to room
    let room = {
        let room_state = ROOM_STATE.lock().map_err(|e| format!("Failed to lock room state: {}", e))?;
        match room_state.as_ref() {
            Some(room) => Arc::clone(room),
            None => return Err("Not connected to a room".to_string()),
        }
    };

    // Get default audio input device
    let host = cpal::default_host();
    let device = host.default_input_device()
        .ok_or_else(|| "No default input device available".to_string())?;

    let device_name = device.name().unwrap_or_else(|_| "Unknown Device".to_string());
    info!("🎤 [LIVEKIT_TAURI] Using audio device: {}", device_name);

    // Get supported config
    let supported_configs = device.supported_input_configs()
        .map_err(|e| format!("Failed to get supported audio configs: {}", e))?;

    let supported_config = supported_configs
        .filter(|config| config.sample_format() == SampleFormat::F32)
        .next()
        .ok_or_else(|| "No supported F32 audio format found".to_string())?;

    let sample_rate = if supported_config.min_sample_rate() <= SampleRate(48000)
        && supported_config.max_sample_rate() >= SampleRate(48000) {
        SampleRate(48000)
    } else {
        supported_config.max_sample_rate()
    };

    let config = StreamConfig {
        channels: supported_config.channels().min(2),
        sample_rate,
        buffer_size: cpal::BufferSize::Default,
    };

    // Create LiveKit audio source
    let sample_rate = 48000u32;
    let num_channels = 1u32;

    let audio_options = AudioSourceOptions {
        echo_cancellation: false,
        noise_suppression: false,
        auto_gain_control: false,
        ..Default::default()
    };
    let native_audio_source = NativeAudioSource::new(
        audio_options,
        sample_rate,
        num_channels,
        1000,
    );
    let audio_source_arc = Arc::new(native_audio_source);

    // Create audio track
    let audio_track = LocalAudioTrack::create_audio_track(
        "microphone",
        RtcAudioSource::Native((*audio_source_arc).clone()),
    );

    // Publish the track
    let track_publication = room.local_participant()
        .publish_track(
            LocalTrack::Audio(audio_track.clone()),
            TrackPublishOptions {
                source: TrackSource::Microphone,
                ..Default::default()
            },
        )
        .await
        .map_err(|e| format!("Failed to publish audio track: {}", e))?;

    info!("✅ [LIVEKIT_TAURI] Audio track published: {:?}", track_publication.sid());

    // Create audio stream with simpler callback approach (like working example)
    let audio_source_for_callback: Arc<NativeAudioSource> = Arc::clone(&audio_source_arc);

    let stream = device.build_input_stream(
        &config,
        move |data: &[f32], _: &cpal::InputCallbackInfo| {
            // Simple audio level monitoring for debugging
            let max_amplitude = data.iter().map(|&x| x.abs()).fold(0.0f32, f32::max);

            // Log first callback to confirm stream is working
            static mut FIRST_CALLBACK: bool = false;
            unsafe {
                if !FIRST_CALLBACK {
                    info!("🎤 [LIVEKIT_TAURI] FIRST AUDIO CALLBACK RECEIVED - Stream is working! Data length: {}", data.len());
                    FIRST_CALLBACK = true;
                }
            }
            
            // Simple audio monitoring (reduced logging)
            static FRAME_COUNTER: AtomicU32 = AtomicU32::new(0);
            static AUDIO_DETECTED_COUNT: AtomicU32 = AtomicU32::new(0);

            let frame_count = FRAME_COUNTER.fetch_add(1, Ordering::Relaxed);
            if max_amplitude > 0.001 {
                AUDIO_DETECTED_COUNT.fetch_add(1, Ordering::Relaxed);
            }

            // Log every 500 frames (~10 seconds) to reduce spam
            if frame_count % 500 == 0 {
                let audio_detected = AUDIO_DETECTED_COUNT.swap(0, Ordering::Relaxed);
                let audio_percentage = (audio_detected as f64 / 500.0) * 100.0;
                info!("🎵 [LIVEKIT_TAURI] Audio activity: {:.1}% frames, max amplitude: {:.6} (Frame #{})",
                      audio_percentage, max_amplitude, frame_count);
            }

            // Convert to mono if needed and normalize
            let mono_data: Vec<f32> = if config.channels == 1 {
                data.to_vec()
            } else {
                // Convert stereo to mono by averaging channels
                data.chunks(config.channels as usize)
                    .map(|chunk| chunk.iter().sum::<f32>() / chunk.len() as f32)
                    .collect()
            };

            // Convert to i16 with proper scaling
            let samples_i16: Vec<i16> = mono_data.iter()
                .map(|&sample| {
                    let clamped = sample.max(-1.0).min(1.0);
                    (clamped * 32767.0) as i16
                })
                .collect();

            // Create AudioFrame for LiveKit
            let samples_per_channel = mono_data.len();
            if samples_per_channel > 0 {
                let audio_frame = AudioFrame {
                    data: std::borrow::Cow::Owned(samples_i16),
                    sample_rate,  // Use the same sample_rate as defined above
                    num_channels, // Use the same num_channels as defined above
                    samples_per_channel: samples_per_channel as u32,
                };

                // Use simple thread spawn approach like working example
                let audio_source_clone = Arc::clone(&audio_source_for_callback);
                std::thread::spawn(move || {
                    let rt = tokio::runtime::Runtime::new().unwrap();
                    rt.block_on(async {
                        if let Err(e) = audio_source_clone.capture_frame(&audio_frame).await {
                            // Only log errors occasionally to avoid spam
                            static ERROR_COUNT: std::sync::atomic::AtomicU32 = std::sync::atomic::AtomicU32::new(0);
                            let count = ERROR_COUNT.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                            if count % 100 == 0 { // Log every 100th error
                                error!("❌ [LIVEKIT_TAURI] Failed to capture audio frame: {}", e);
                            }
                        }
                    });
                });
            }
        },
        |err| {
            error!("❌ [LIVEKIT_TAURI] Audio stream error: {}", err);
        },
        None,
    ).map_err(|e| format!("Failed to build input stream: {}", e))?;

    // Start the stream
    stream.play().map_err(|e| format!("Failed to start audio stream: {}", e))?;

    info!("🔒 [LIVEKIT_TAURI] Stream started successfully");

    // Since cpal::Stream is not Send/Sync, we cannot move it to another thread or store it safely
    // The only reliable way to ensure cleanup is to let the process handle it on exit
    // We use std::mem::forget to keep the stream alive, but set a flag for tracking
    info!("💾 [LIVEKIT_TAURI] Stream will be managed by process - cleanup on process exit");
    
    // Create shutdown channel for signaling (for potential future use)
    let (shutdown_tx, _shutdown_rx) = mpsc::channel();
    
    // Store shutdown sender for cleanup functions
    {
        let mut shutdown_sender = AUDIO_STREAM_SHUTDOWN.lock().map_err(|e| format!("Failed to lock audio stream shutdown: {}", e))?;
        *shutdown_sender = Some(shutdown_tx);
    }
    
    // Keep stream alive - it will be cleaned up when process exits
    std::mem::forget(stream);


    // Store in global state
    {
        let mut audio_source_state = AUDIO_SOURCE.lock().map_err(|e| format!("Failed to lock audio source: {}", e))?;
        *audio_source_state = Some(audio_source_arc);

        let mut audio_track_state = AUDIO_TRACK.lock().map_err(|e| format!("Failed to lock audio track: {}", e))?;
        *audio_track_state = Some(audio_track);

        let mut audio_stream_active_state = AUDIO_STREAM_ACTIVE.lock().map_err(|e| format!("Failed to lock audio stream active: {}", e))?;
        *audio_stream_active_state = true;

        let mut publishing = AUDIO_PUBLISHING.lock().map_err(|e| format!("Failed to lock audio publishing: {}", e))?;
        *publishing = true;
    }

    info!("🎉 [LIVEKIT_TAURI] Audio publishing started successfully!");

    Ok(AudioPublishStatus {
        is_publishing: true,
        error: None,
        device_name: Some(device_name),
    })
}

#[command]
pub async fn stop_audio_publishing() -> Result<AudioPublishStatus, String> {
    info!("🛑 [LIVEKIT_TAURI] Stopping audio publishing...");

    // Check if currently publishing
    {
        let publishing = AUDIO_PUBLISHING.lock().map_err(|e| format!("Failed to lock audio state: {}", e))?;
        if !*publishing {
            return Ok(AudioPublishStatus {
                is_publishing: false,
                error: None,
                device_name: None,
            });
        }
    }

    // Get room reference
    let room = {
        let room_state = ROOM_STATE.lock().map_err(|e| format!("Failed to lock room state: {}", e))?;
        match room_state.as_ref() {
            Some(room) => Arc::clone(room),
            None => return Err("Not connected to a room".to_string()),
        }
    };

    // Since cpal::Stream cannot be properly cleaned up without process exit,
    // we need to take more drastic action to release the microphone
    info!("🛑 [LIVEKIT_TAURI] Stream cleanup requested - forcing process cleanup to release microphone");
    
    // Clear the shutdown sender to indicate cleanup was attempted
    {
        let mut shutdown_sender = AUDIO_STREAM_SHUTDOWN.lock().map_err(|e| format!("Failed to lock audio stream shutdown: {}", e))?;
        *shutdown_sender = None;
    }
    
    // The stream will only be properly released when the process exits
    // This is a limitation of cpal::Stream not being Send/Sync
    info!("⚠️ [LIVEKIT_TAURI] Stream will be released when process exits due to cpal limitations");
    
    // Try to find and terminate only audio-related threads for this specific process
    info!("🔧 [LIVEKIT_TAURI] Attempting safe audio device release...");
    
    // Get current process ID for targeted cleanup
    let current_pid = std::process::id();
    info!("� [LIVEKIT_TAURI] Current process ID: {}", current_pid);
    
    // Try to identify audio device usage by this specific process
    std::thread::spawn(move || {
        std::thread::sleep(std::time::Duration::from_millis(500));
        if let Ok(output) = std::process::Command::new("lsof")
            .arg("+D")
            .arg("/dev/snd")
            .arg("-p")
            .arg(&current_pid.to_string())
            .output()
        {
            let output_str = String::from_utf8_lossy(&output.stdout);
            if !output_str.is_empty() {
                info!("🎤 [LIVEKIT_TAURI] Audio device usage detected: {}", output_str);
            } else {
                info!("✅ [LIVEKIT_TAURI] No audio device usage found for this process");
        // Use lsof to find audio device usage by this specific process
        
            }
        } else {
            warn!("⚠️ [LIVEKIT_TAURI] Could not check audio device usage (lsof not available)");
        }
    });
    
    info!("📝 [LIVEKIT_TAURI] Safe audio device release initiated");

    // Unpublish audio track
    if let Some(audio_track) = {
        let mut audio_track_state = AUDIO_TRACK.lock().map_err(|e| format!("Failed to lock audio track: {}", e))?;
        audio_track_state.take()
    } {
        if let Err(e) = room.local_participant().unpublish_track(&audio_track.sid()).await {
            warn!("⚠️ [LIVEKIT_TAURI] Failed to unpublish audio track: {}", e);
        } else {
            info!("✅ [LIVEKIT_TAURI] Audio track unpublished successfully");
        }
    }

    // Clear audio source - the stream cleanup happens via shutdown signal above
    {
        let mut audio_source_state = AUDIO_SOURCE.lock().map_err(|e| format!("Failed to lock audio source: {}", e))?;
        *audio_source_state = None;
    }
    
    info!("✅ [LIVEKIT_TAURI] Audio source cleared and shutdown signal sent - microphone should be released");

    // Mark audio stream as inactive
    {
        let mut audio_stream_active_state = AUDIO_STREAM_ACTIVE.lock().map_err(|e| format!("Failed to lock audio stream active: {}", e))?;
        *audio_stream_active_state = false;
        info!("✅ [LIVEKIT_TAURI] Audio stream marked as inactive");
    }

    // Cleanup state - this is crucial for releasing the microphone
    {
        let mut audio_source_state = AUDIO_SOURCE.lock().map_err(|e| format!("Failed to lock audio source: {}", e))?;
        if let Some(source) = audio_source_state.take() {
            info!("🛑 [LIVEKIT_TAURI] Dropping audio source - this should trigger microphone release");
            drop(source); // Explicitly drop the audio source
        }

        let mut publishing = AUDIO_PUBLISHING.lock().map_err(|e| format!("Failed to lock audio publishing: {}", e))?;
        *publishing = false;
    }

    info!("✅ [LIVEKIT_TAURI] Audio publishing stopped and all resources cleaned up");

    Ok(AudioPublishStatus {
        is_publishing: false,
        error: None,
        device_name: None,
    })
}

#[command]
pub async fn get_audio_publish_status() -> Result<AudioPublishStatus, String> {
    let publishing = AUDIO_PUBLISHING.lock().map_err(|e| format!("Failed to lock audio state: {}", e))?;
    Ok(AudioPublishStatus {
        is_publishing: *publishing,
        error: None,
        device_name: if *publishing { Some("Microphone".to_string()) } else { None },
    })
}

// Audio processing functions
fn process_audio_frame(audio_frame: &AudioFrame) -> Vec<f32> {
    let _input_sample_rate = audio_frame.sample_rate;
    let input_channels = audio_frame.num_channels;
    let _target_sample_rate = 48000u32;
    let target_channels = 2u32;

    // Convert i16 to f32
    let mut f32_samples: Vec<f32> = audio_frame.data.iter()
        .map(|&sample| {
            let normalized = sample as f32 / 32768.0;
            normalized.max(-1.0).min(1.0)
        })
        .collect();

    // Handle channel conversion
    if input_channels == 1 && target_channels == 2 {
        // Mono to stereo
        let mut stereo_samples = Vec::with_capacity(f32_samples.len() * 2);
        for sample in f32_samples {
            stereo_samples.push(sample);
            stereo_samples.push(sample);
        }
        f32_samples = stereo_samples;
    } else if input_channels == 2 && target_channels == 2 {
        // Already stereo
    } else if input_channels > 2 {
        // Multi-channel to stereo
        let samples_per_channel = f32_samples.len() / input_channels as usize;
        let mut stereo_samples = Vec::with_capacity(samples_per_channel * 2);

        for i in 0..samples_per_channel {
            let left = f32_samples[i * input_channels as usize];
            let right = if input_channels > 1 {
                f32_samples[i * input_channels as usize + 1]
            } else {
                left
            };
            stereo_samples.push(left);
            stereo_samples.push(right);
        }
        f32_samples = stereo_samples;
    }

    // Apply gentle volume normalization
    let max_amplitude = f32_samples.iter().map(|&x| x.abs()).fold(0.0f32, f32::max);
    if max_amplitude > 0.001 {
        let target_level = 0.7f32;
        let compression_ratio = if max_amplitude > target_level {
            target_level / max_amplitude
        } else {
            1.0
        };

        for sample in f32_samples.iter_mut() {
            *sample *= compression_ratio;
            if sample.abs() < 0.001 {
                *sample *= 0.5;
            }
        }
    }

    f32_samples
}

fn start_audio_playback() -> Result<(), String> {
    info!("🔊 [LIVEKIT_TAURI] Starting audio playback system...");

    // Check if remote audio is muted - if so, skip starting audio playback
    let is_muted = {
        let muted = REMOTE_AUDIO_MUTED.lock().map_err(|e| format!("Failed to lock remote audio muted state: {}", e))?;
        *muted
    };
    
    if is_muted {
        info!("🔇 [LIVEKIT_TAURI] Remote audio is muted, skipping audio playback startup");
        return Ok(());
    }

    // CRITICAL FIX: Remove the early return check that was preventing audio playback restart
    // This was the root cause of the issue - once marked as active, playback couldn't restart
    // for new participants or after interruptions

    // Check current state for logging purposes only
    let was_already_active = {
        let stream_active = AUDIO_STREAM_ACTIVE.lock().map_err(|e| format!("Failed to lock stream active: {}", e))?;
        *stream_active
    };

    if was_already_active {
        info!("🔄 [LIVEKIT_TAURI] Audio playback was already marked as active, but restarting to ensure proper operation...");
    } else {
        info!("🆕 [LIVEKIT_TAURI] Starting fresh audio playback system...");
    }

    let host = cpal::default_host();
    let output_device = host.default_output_device()
        .ok_or_else(|| "No audio output device found".to_string())?;

    info!("🎧 [LIVEKIT_TAURI] Using audio output device: {}", output_device.name().unwrap_or("Unknown".to_string()));

    let config = cpal::StreamConfig {
        channels: 2,
        sample_rate: cpal::SampleRate(48000),
        buffer_size: cpal::BufferSize::Fixed(1024),
    };

    // Add debug logging for buffer state
    let buffer_size = {
        if let Ok(buffer) = AUDIO_PLAYBACK_BUFFER.lock() {
            buffer.len()
        } else {
            0
        }
    };
    info!("📊 [LIVEKIT_TAURI] Current audio buffer size: {} samples", buffer_size);

    let output_stream = output_device.build_output_stream(
        &config,
        move |data: &mut [f32], _: &cpal::OutputCallbackInfo| {
            if let Ok(mut buffer) = AUDIO_PLAYBACK_BUFFER.lock() {
                let mut samples_played = 0;
                for sample in data.iter_mut() {
                    if let Some(audio_sample) = buffer.pop_front() {
                        *sample = audio_sample;
                        samples_played += 1;
                    } else {
                        *sample = 0.0;
                    }
                }

                // Log audio activity periodically for debugging
                static mut CALLBACK_COUNT: u32 = 0;
                unsafe {
                    CALLBACK_COUNT += 1;
                    if CALLBACK_COUNT % 1000 == 0 { // Log every ~20 seconds at 48kHz
                        if samples_played > 0 {
                            info!("🔊 [LIVEKIT_TAURI] Audio playback active: {} samples played, buffer size: {}",
                                  samples_played, buffer.len());
                        }
                    }
                }
            } else {
                data.fill(0.0);
            }
        },
        |err| error!("❌ [LIVEKIT_TAURI] Audio output error: {}", err),
        None,
    ).map_err(|e| format!("Failed to create output stream: {}", e))?;

    output_stream.play().map_err(|e| format!("Failed to start output stream: {}", e))?;

    // Mark stream as active
    {
        let mut stream_active = AUDIO_STREAM_ACTIVE.lock().map_err(|e| format!("Failed to lock stream active: {}", e))?;
        *stream_active = true;
    }

    // Keep the stream alive by forgetting it
    // This is necessary because CPAL streams must remain alive to continue playing audio
    // The stream will be cleaned up when the application exits
    mem::forget(output_stream);

    info!("✅ [LIVEKIT_TAURI] Audio playback started successfully - stream will remain active!");
    Ok(())
}

/// Stop audio playback and cleanup resources
fn stop_audio_playback() -> Result<(), String> {
    info!("🔇 [LIVEKIT_TAURI] Stopping audio playback system...");

    // Clear the playback buffer
    {
        let mut buffer = AUDIO_PLAYBACK_BUFFER.lock().map_err(|e| format!("Failed to lock playback buffer: {}", e))?;
        buffer.clear();
        info!("🧹 [LIVEKIT_TAURI] Audio playback buffer cleared");
    }

    // Mark stream as inactive
    {
        let mut stream_active = AUDIO_STREAM_ACTIVE.lock().map_err(|e| format!("Failed to lock stream active: {}", e))?;
        *stream_active = false;
    }

    info!("✅ [LIVEKIT_TAURI] Audio playback stopped and cleaned up");
    info!("ℹ️ [LIVEKIT_TAURI] Note: Audio stream was forgotten and will remain active until app shutdown");
    Ok(())
}

/// Manual command to restart audio playback for debugging/testing
#[command]
pub async fn restart_audio_playback() -> Result<String, String> {
    info!("🔄 [LIVEKIT_TAURI] Manual restart of audio playback requested...");

    // Check current buffer state
    let buffer_info = {
        if let Ok(buffer) = AUDIO_PLAYBACK_BUFFER.lock() {
            format!("Buffer size: {} samples", buffer.len())
        } else {
            "Buffer unavailable".to_string()
        }
    };

    // Restart audio playback
    match start_audio_playback() {
        Ok(()) => {
            let success_msg = format!("✅ Audio playback restarted successfully. {}", buffer_info);
            info!("{}", success_msg);
            Ok(success_msg)
        },
        Err(e) => {
            let error_msg = format!("❌ Failed to restart audio playback: {}. {}", e, buffer_info);
            error!("{}", error_msg);
            Err(error_msg)
        }
    }
}

/// Test function to verify LiveKit token request
#[command]
pub async fn test_livekit_connection() -> Result<String, String> {
    println!("🧪 [LIVEKIT_TAURI] Testing LiveKit connection...");
    
    let test_result = get_livekit_token(
        "test_user".to_string(),
        1234,
        "test_room".to_string(),
        "screen_view".to_string(),
        Some("test_metadata".to_string()),
    ).await;

    match test_result {
        Ok(token) => {
            println!("✅ [LIVEKIT_TAURI] Test successful - token received");
            Ok(format!("Test successful - token length: {}", token.len()))
        }
        Err(e) => {
            println!("❌ [LIVEKIT_TAURI] Test failed: {}", e);
            Err(format!("Test failed: {}", e))
        }
    }
}

// Initialize app handle for emitting events
#[command]
pub async fn init_livekit_events(app: tauri::AppHandle) -> Result<(), String> {
    info!("🔧 [LIVEKIT_TAURI] Initializing app handle for events");
    
    if let Ok(mut handle) = APP_HANDLE.lock() {
        *handle = Some(app);
        info!("✅ [LIVEKIT_TAURI] App handle initialized successfully");
    } else {
        error!("❌ [LIVEKIT_TAURI] Failed to set app handle");
        return Err("Failed to set app handle".to_string());
    }
    
    Ok(())
}

// Helper function to notify React about participant changes
async fn notify_participant_change(event_type: &str, participant: &livekit::participant::RemoteParticipant) {
    info!("📡 [LIVEKIT_TAURI] Notifying React about participant {}: {}", event_type, participant.identity());
    
    if let Ok(app_handle) = APP_HANDLE.lock() {
        if let Some(app) = app_handle.as_ref() {
            let participant_data = serde_json::json!({
                "event": event_type,
                "participant": {
                    "sid": participant.sid().to_string(),
                    "identity": participant.identity().to_string(),
                    "name": participant.name(),
                    "metadata": participant.metadata(),
                    "is_local": false
                }
            });
            
            if let Err(e) = app.emit("livekit-participant-change", &participant_data) {
                warn!("⚠️ [LIVEKIT_TAURI] Failed to emit participant change event: {}", e);
            } else {
                info!("📤 [LIVEKIT_TAURI] Participant change event emitted successfully");
            }
        } else {
            warn!("⚠️ [LIVEKIT_TAURI] App handle not available for emitting events");
        }
    } else {
        warn!("⚠️ [LIVEKIT_TAURI] Failed to lock app handle for emitting events");
    }
}

/// Mute remote audio playback (no more sound from remote participants)
#[command]
pub async fn mute_remote_audio() -> Result<String, String> {
    info!("🔇 [LIVEKIT_TAURI] Muting remote audio playback...");
    
    // Set the muted flag
    if let Ok(mut muted) = REMOTE_AUDIO_MUTED.lock() {
        *muted = true;
        info!("✅ [LIVEKIT_TAURI] Remote audio muted successfully");
        
        // Stop the audio playback system
        if let Err(e) = stop_audio_playback() {
            warn!("⚠️ [LIVEKIT_TAURI] Failed to stop audio playback when muting: {}", e);
        }
        
        Ok("Remote audio muted".to_string())
    } else {
        let error_msg = "Failed to lock remote audio muted state";
        error!("❌ [LIVEKIT_TAURI] {}", error_msg);
        Err(error_msg.to_string())
    }
}

/// Unmute remote audio playback (restore sound from remote participants)
#[command]
pub async fn unmute_remote_audio() -> Result<String, String> {
    info!("🔊 [LIVEKIT_TAURI] Unmuting remote audio playback...");
    
    // Clear the muted flag
    if let Ok(mut muted) = REMOTE_AUDIO_MUTED.lock() {
        *muted = false;
        info!("✅ [LIVEKIT_TAURI] Remote audio unmuted successfully");
        
        // Restart the audio playback system
        if let Err(e) = start_audio_playback() {
            warn!("⚠️ [LIVEKIT_TAURI] Failed to start audio playback when unmuting: {}", e);
            return Err(format!("Failed to start audio playback: {}", e));
        }
        
        Ok("Remote audio unmuted".to_string())
    } else {
        let error_msg = "Failed to lock remote audio muted state";
        error!("❌ [LIVEKIT_TAURI] {}", error_msg);
        Err(error_msg.to_string())
    }
}

// ===== TESTS =====
#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use std::thread;

    #[test]
    fn test_audio_playback_stream_persistence() {
        // Test that audio playback stream remains active after function returns
        let result = start_audio_playback();
        assert!(result.is_ok(), "Audio playback should start successfully");
        
        // Check that stream is marked as active
        let stream_active = {
            let active = AUDIO_STREAM_ACTIVE.lock().expect("Should lock stream active state");
            *active
        };
        assert!(stream_active, "Audio stream should be marked as active after starting");
        
        // Simulate some audio data in buffer
        {
            let mut buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            for i in 0..1000 {
                buffer.push_back((i as f32 / 1000.0) * 0.1); // Small sine wave data
            }
        }
        
        // Wait a bit to ensure stream continues working
        thread::sleep(Duration::from_millis(100));
        
        // Stream should still be active
        let stream_still_active = {
            let active = AUDIO_STREAM_ACTIVE.lock().expect("Should lock stream active state");
            *active
        };
        assert!(stream_still_active, "Audio stream should still be active after time passes");
        
        // Cleanup buffer only (stream is forgotten)
        let _ = stop_audio_playback();
    }

    #[test]
    fn test_audio_playback_buffer_consumption() {
        // Test that audio buffer is properly consumed during playback
        
        // Add test data to buffer
        {
            let mut buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            for i in 0..2000 {
                buffer.push_back((i as f32 / 2000.0) * 0.2);
            }
        }
        
        let initial_buffer_size = {
            let buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            buffer.len()
        };
        
        // Start playback
        let result = start_audio_playback();
        assert!(result.is_ok(), "Audio playback should start successfully");
        
        // Wait for buffer to be consumed
        thread::sleep(Duration::from_millis(200));
        
        let final_buffer_size = {
            let buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            buffer.len()  
        };
        
        // Buffer should be consumed (reduced) during playback
        assert!(final_buffer_size < initial_buffer_size, 
                "Audio buffer should be consumed during playback. Initial: {}, Final: {}", 
                initial_buffer_size, final_buffer_size);
        
        // Cleanup
        let _ = stop_audio_playback();
    }
    
    #[test]
    fn test_process_audio_frame_conversion() {
        // Test audio frame processing
        let test_data: Vec<i16> = vec![1000, -1000, 2000, -2000]; // Test stereo data
        let audio_frame = AudioFrame {
            data: test_data.into(),
            sample_rate: 48000,
            num_channels: 2,
            samples_per_channel: 2,
        };
        
        let processed = process_audio_frame(&audio_frame);
        
        // Should have same number of samples (stereo input = stereo output)
        assert_eq!(processed.len(), 4, "Should maintain stereo sample count");
        
        // Check normalization (i16 to f32)
        assert!(processed[0] > 0.0 && processed[0] < 1.0, "Should normalize positive samples correctly");
        assert!(processed[1] < 0.0 && processed[1] > -1.0, "Should normalize negative samples correctly");
    }
    
    #[test]
    fn test_audio_playback_cleanup() {
        // Test proper cleanup of audio resources
        
        // Start playback
        let result = start_audio_playback();
        assert!(result.is_ok(), "Audio playback should start successfully");
        
        // Verify it's active
        let is_active = {
            let active = AUDIO_STREAM_ACTIVE.lock().expect("Should lock stream active state");
            *active
        };
        assert!(is_active, "Stream should be active after starting");
        
        // Stop playback
        let stop_result = stop_audio_playback();
        assert!(stop_result.is_ok(), "Audio playback should stop successfully");
        
        // Verify it's inactive
        let is_inactive = {
            let active = AUDIO_STREAM_ACTIVE.lock().expect("Should lock stream active state");
            !*active
        };
        assert!(is_inactive, "Stream should be inactive after stopping");
        
        // Verify buffer is cleared
        let buffer_empty = {
            let buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            buffer.is_empty()
        };
        assert!(buffer_empty, "Audio buffer should be empty after cleanup");
    }
}
