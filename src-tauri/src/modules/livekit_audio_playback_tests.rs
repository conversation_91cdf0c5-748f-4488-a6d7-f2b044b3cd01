#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use std::thread;

    #[tokio::test]
    async fn test_audio_playback_stream_persistence() {
        // Test that audio playback stream remains active after function returns
        let result = start_audio_playback();
        assert!(result.is_ok(), "Audio playback should start successfully");
        
        // Check that stream is marked as active
        let stream_active = {
            let active = AUDIO_STREAM_ACTIVE.lock().expect("Should lock stream active state");
            *active
        };
        assert!(stream_active, "Audio stream should be marked as active after starting");
        
        // Simulate some audio data in buffer
        {
            let mut buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            for i in 0..1000 {
                buffer.push_back((i as f32 / 1000.0) * 0.1); // Small sine wave data
            }
        }
        
        // Wait a bit to ensure stream doesn't get dropped
        thread::sleep(Duration::from_millis(100));
        
        // Stream should still be active
        let stream_still_active = {
            let active = AUDIO_STREAM_ACTIVE.lock().expect("Should lock stream active state");
            *active
        };
        assert!(stream_still_active, "Audio stream should still be active after time passes");
    }

    #[tokio::test] 
    async fn test_audio_playback_buffer_consumption() {
        // Test that audio buffer is properly consumed during playback
        
        // Add test data to buffer
        {
            let mut buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            for i in 0..2000 {
                buffer.push_back((i as f32 / 2000.0) * 0.2);
            }
        }
        
        let initial_buffer_size = {
            let buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            buffer.len()
        };
        
        // Start playback
        let result = start_audio_playback();
        assert!(result.is_ok(), "Audio playback should start successfully");
        
        // Wait for buffer to be consumed
        thread::sleep(Duration::from_millis(200));
        
        let final_buffer_size = {
            let buffer = AUDIO_PLAYBACK_BUFFER.lock().expect("Should lock audio buffer");
            buffer.len()  
        };
        
        // Buffer should be consumed (reduced) during playback
        assert!(final_buffer_size < initial_buffer_size, 
                "Audio buffer should be consumed during playback. Initial: {}, Final: {}", 
                initial_buffer_size, final_buffer_size);
    }
    
    #[test]
    fn test_process_audio_frame_conversion() {
        // Test audio frame processing
        let test_data: Vec<i16> = vec![1000, -1000, 2000, -2000]; // Test stereo data
        let audio_frame = AudioFrame {
            data: test_data,
            sample_rate: 48000,
            num_channels: 2,
            samples_per_channel: 2,
        };
        
        let processed = process_audio_frame(&audio_frame);
        
        // Should have same number of samples (stereo input = stereo output)
        assert_eq!(processed.len(), 4, "Should maintain stereo sample count");
        
        // Check normalization (i16 to f32)
        assert!(processed[0] > 0.0 && processed[0] < 1.0, "Should normalize positive samples correctly");
        assert!(processed[1] < 0.0 && processed[1] > -1.0, "Should normalize negative samples correctly");
    }
}