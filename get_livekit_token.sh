#!/bin/bash

# LiveKit Token Generator Script
# Usage: ./get_livekit_token.sh <room_name>

# Check if room name is provided
if [ $# -eq 0 ]; then
    echo "❌ Error: Room name is required"
    echo "Usage: $0 <room_name>"
    exit 1
fi

ROOM_NAME="$1"

# Generate random values similar to Rust implementation
# Using current timestamp for randomness like in the Rust code
TIMESTAMP=$(date +%s%3N)  # milliseconds
RANDOM_USER_ID=$((TIMESTAMP % 10000))
RANDOM_NAME_SUFFIX=$((RANDOM % 1000))
NAME="user_${RANDOM_NAME_SUFFIX}"
USER_ID="user_${RANDOM_USER_ID}@example.com"
USERNAME="${NAME}${RANDOM_USER_ID}"
EVENT_TYPE="screen_view"

# Prepare metadata as JSON string (will be stringified in final payload)
METADATA='{"id":31,"name":"<PERSON><PERSON><PERSON>","avatar":"http://127.0.0.1:8000/media/user_avatars/logo2.png"}'

echo "🔧 [LIVEKIT_BASH] Making token request with params:"
echo "  - name: ${NAME}"
echo "  - user_id: ${USER_ID}"
echo "  - room_name: ${ROOM_NAME}"
echo "  - event_type: ${EVENT_TYPE}"
echo "  - username: ${USERNAME}"
echo "  - random_user_id: ${RANDOM_USER_ID}"
echo "  - metadata: ${METADATA}"

# Use jq to properly construct JSON payload with escaped metadata string
if command -v jq &> /dev/null; then
    JSON_PAYLOAD=$(jq -n \
        --arg event "${EVENT_TYPE}" \
        --arg live_session_id "${ROOM_NAME}" \
        --arg subject "null" \
        --argjson user_id ${RANDOM_USER_ID} \
        --arg username "${USERNAME}" \
        --arg can_publish "true" \
        --arg metadata "${METADATA}" \
        '{
            "event": $event,
            "live_session_id": $live_session_id,
            "subject": $subject,
            "user_id": $user_id,
            "username": $username,
            "can_publish": $can_publish,
            "metadata": $metadata
        }')
else
    # Fallback: manually escape quotes in metadata for JSON
    METADATA_ESCAPED=$(echo "${METADATA}" | sed 's/"/\\"/g')
    JSON_PAYLOAD=$(cat <<EOF
{
    "event": "${EVENT_TYPE}",
    "live_session_id": "${ROOM_NAME}",
    "subject": "null",
    "user_id": ${RANDOM_USER_ID},
    "username": "${USERNAME}",
    "can_publish": "true",
    "metadata": "${METADATA_ESCAPED}"
}
EOF
    )
fi

echo "🌐 [LIVEKIT_BASH] Sending request to token server..."

# Make HTTP request using curl with error handling
TEMP_FILE=$(mktemp)
HTTP_CODE=$(curl -s -w "%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -d "${JSON_PAYLOAD}" \
    -o "${TEMP_FILE}" \
    "https://habibmeet.nwhco.ir/test/sfu/token")

RESPONSE_BODY=$(cat "${TEMP_FILE}")
rm "${TEMP_FILE}"

# Check if curl command succeeded
if [ $? -ne 0 ]; then
    echo "❌ [LIVEKIT_BASH] Failed to send request - network error"
    exit 1
fi

# Check HTTP status
if [ "${HTTP_CODE}" != "200" ]; then
    echo "❌ [LIVEKIT_BASH] HTTP error! status: ${HTTP_CODE}"
    echo "Response: ${RESPONSE_BODY}"
    exit 1
fi

# Parse JSON response to extract token using jq if available, otherwise use grep
if command -v jq &> /dev/null; then
    TOKEN=$(echo "${RESPONSE_BODY}" | jq -r '.token')
else
    # Fallback to grep/sed if jq is not available
    TOKEN=$(echo "${RESPONSE_BODY}" | grep -o '"token":"[^"]*"' | sed 's/"token":"//' | sed 's/"//')
fi

# Validate token extraction
if [ -z "${TOKEN}" ] || [ "${TOKEN}" = "null" ]; then
    echo "❌ [LIVEKIT_BASH] Failed to parse token from response"
    echo "Response: ${RESPONSE_BODY}"
    exit 1
fi

echo "✅ [LIVEKIT_BASH] Token received successfully"
echo "🔧 [LIVEKIT_BASH] Token length: ${#TOKEN}"
echo ""
echo "🎯 LiveKit Token:"
echo "${TOKEN}"

# Exit with success
exit 0
