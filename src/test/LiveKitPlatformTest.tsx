/**
 * Test component to verify LiveKit platform detection and service initialization
 */

import React, { useEffect, useState } from 'react';
import { useLiveKitPlatform } from '../hooks/useLiveKitPlatform';

export const LiveKitPlatformTest: React.FC = () => {
  const liveKit = useLiveKitPlatform();
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    const runTests = async () => {
      const results: string[] = [];

      // Test 1: Platform detection
      results.push(`✅ Platform detection initialized: ${liveKit.isInitialized}`);
      results.push(`🖥️ Detected platform: ${liveKit.platform}`);
      results.push(`🐧 Is Linux: ${liveKit.isLinux}`);

      if (liveKit.isInitialized) {
        try {
          // Test 2: Connection status (should work without actual connection)
          const status = await liveKit.getConnectionStatus();
          results.push(`✅ Connection status check: ${JSON.stringify(status)}`);
        } catch (error) {
          results.push(`❌ Connection status check failed: ${error}`);
        }

        try {
          // Test 3: Audio publish status (should work without actual connection)
          const audioStatus = await liveKit.getAudioPublishStatus();
          results.push(`✅ Audio publish status check: ${audioStatus}`);
        } catch (error) {
          results.push(`❌ Audio publish status check failed: ${error}`);
        }

        try {
          // Test 4: Get participants (should return empty array when not connected)
          const participants = await liveKit.getRoomParticipants();
          results.push(`✅ Participants check: ${participants.length} participants`);
        } catch (error) {
          results.push(`❌ Participants check failed: ${error}`);
        }

        // Test 5: Platform-specific implementation check
        if (liveKit.isLinux) {
          results.push(`✅ Using Rust implementation for Linux`);
        } else {
          results.push(`✅ Using JavaScript implementation for ${liveKit.platform}`);
        }
      }

      setTestResults(results);
    };

    if (liveKit.isInitialized) {
      runTests();
    }
  }, [liveKit.isInitialized, liveKit.platform, liveKit.isLinux, liveKit]);

  return (
    <div style={{ padding: '20px', backgroundColor: '#1a1a2e', color: 'white', fontFamily: 'monospace' }}>
      <h2>LiveKit Platform Test Results</h2>
      <div style={{ marginTop: '20px' }}>
        {testResults.length === 0 ? (
          <p>Running tests...</p>
        ) : (
          testResults.map((result, index) => (
            <div key={index} style={{ marginBottom: '8px', fontSize: '14px' }}>
              {result}
            </div>
          ))
        )}
      </div>
      
      {liveKit.isInitialized && (
        <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#2a2d3c', borderRadius: '8px' }}>
          <h3>Platform Summary</h3>
          <p><strong>Platform:</strong> {liveKit.platform}</p>
          <p><strong>Implementation:</strong> {liveKit.isLinux ? 'Rust LiveKit SDK' : 'JavaScript LiveKit Client'}</p>
          <p><strong>Status:</strong> {liveKit.isInitialized ? 'Ready' : 'Initializing...'}</p>
        </div>
      )}
    </div>
  );
};
