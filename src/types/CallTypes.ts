// Types for Call Window functionality

import type { TeamMember } from './TeamMember';

export interface CallWindowData {
  caller: Team<PERSON><PERSON>ber;
  callee: TeamMember;
  timestamp: number;
  source: 'team-sidebar';
  callId?: string;
}

export interface CallParticipant {
  user: TeamMember;
  status: 'ringing' | 'connected' | 'rejected';
  joinedAt?: number;
  isLocal?: boolean; // Indicates if this is the local participant
}

export interface CallState {
  status: 'ringing' | 'connected' | 'ended' | 'rejected';
  duration: number;
  participants: CallParticipant[];
  isMuted: boolean;
  isSpeakerOn: boolean;
  isVideoOn?: boolean;
}

// For future WebRTC integration
export interface CallSettings {
  audioEnabled: boolean;
  videoEnabled: boolean;
  screenShareEnabled: boolean;
  recordingEnabled?: boolean;
}
