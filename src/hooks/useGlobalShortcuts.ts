import { useEffect } from 'react';
import { register, unregister, isRegistered } from '@tauri-apps/plugin-global-shortcut';
import { invoke } from '@tauri-apps/api/core';

export const useGlobalShortcuts = () => {
  useEffect(() => {
    const registerShortcuts = async () => {
      try {
        console.log('⌨️ [SHORTCUT] Starting global shortcuts registration...');
        
        // Add a small delay to ensure <PERSON><PERSON> is fully initialized
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('⌨️ [SHORTCUT] Tauri initialization delay complete');
        
        // Check if we have permissions
        try {
          // Test registration with a simple shortcut first
          const testShortcut = 'CommandOrControl+Shift+T';
          await register(testShortcut, () => {
            console.log('🧪 [SHORTCUT] Test shortcut triggered');
          });
          
          const isTestRegistered = await isRegistered(testShortcut);
          console.log('🧪 [SHORTCUT] Test shortcut registered:', isTestRegistered);
          
          if (isTestRegistered) {
            await unregister(testShortcut);
            console.log('🧪 [SHORTCUT] Test shortcut unregistered successfully');
          }
        } catch (testError) {
          console.error('❌ [SHORTCUT] Test registration failed:', testError);
          return; // Exit if we can't even register a test shortcut
        }

        // Register CommandOrControl+X for clearing all user data and reloading
        console.log('⌨️ [SHORTCUT] Registering CommandOrControl+X...');
        await register('CommandOrControl+X', async (event) => {
          console.log('⌨️ [SHORTCUT] CommandOrControl+X triggered with event:', event);
          console.log('⌨️ [SHORTCUT] Event type:', typeof event);
          console.log('⌨️ [SHORTCUT] Event keys:', Object.keys(event || {}));
          
          // Handle different event structures
          const shouldTrigger = !event || event.state === 'Pressed' || event.type === 'KeyDown' || typeof event === 'string';
          
          if (shouldTrigger) {
            console.log('⌨️ [SHORTCUT] CommandOrControl+X pressed - clearing all user data and reloading...');
            try {
              // Clear all user data
              await invoke('clear_all_user_data');
              console.log('✅ [SHORTCUT] User data cleared, reloading app...');
              
              // Reload the app
              await invoke('reload_window');
            } catch (error) {
              console.error('❌ [SHORTCUT] Failed to clear data and reload:', error);
            }
          } else {
            console.log('⌨️ [SHORTCUT] CommandOrControl+X event ignored (not pressed state)');
          }
        });

        // Check if CommandOrControl+X was registered
        const isCtrlXRegistered = await isRegistered('CommandOrControl+X');
        console.log('⌨️ [SHORTCUT] CommandOrControl+X registered:', isCtrlXRegistered);

        // Register CommandOrControl+R for reloading windows
        console.log('⌨️ [SHORTCUT] Registering CommandOrControl+R...');
        await register('CommandOrControl+R', async (event) => {
          console.log('⌨️ [SHORTCUT] CommandOrControl+R triggered with event:', event);
          console.log('⌨️ [SHORTCUT] Event type:', typeof event);
          console.log('⌨️ [SHORTCUT] Event keys:', Object.keys(event || {}));
          
          // Handle different event structures
          const shouldTrigger = !event || event.state === 'Pressed' || event.type === 'KeyDown' || typeof event === 'string';
          
          if (shouldTrigger) {
            console.log('⌨️ [SHORTCUT] CommandOrControl+R pressed - reloading windows...');
            try {
              await invoke('reload_window');
              console.log('✅ [SHORTCUT] Window reload initiated');
            } catch (error) {
              console.error('❌ [SHORTCUT] Failed to reload window:', error);
            }
          } else {
            console.log('⌨️ [SHORTCUT] CommandOrControl+R event ignored (not pressed state)');
          }
        });

        // Check if CommandOrControl+R was registered  
        const isCtrlRRegistered = await isRegistered('CommandOrControl+R');
        console.log('⌨️ [SHORTCUT] CommandOrControl+R registered:', isCtrlRRegistered);

        console.log('✅ [SHORTCUT] Global shortcuts registration complete');
        console.log('✅ [SHORTCUT] Registered shortcuts:', {
          'CommandOrControl+X': isCtrlXRegistered,
          'CommandOrControl+R': isCtrlRRegistered
        });
      } catch (error) {
        console.error('❌ [SHORTCUT] Failed to register shortcuts:', error);
        console.error('❌ [SHORTCUT] Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }
    };

    registerShortcuts();

    // Cleanup function to unregister shortcuts
    return () => {
      const cleanup = async () => {
        try {
          console.log('🧹 [SHORTCUT] Starting cleanup...');
          
          // Check what's registered before cleanup
          const ctrlXRegistered = await isRegistered('CommandOrControl+X');
          const ctrlRRegistered = await isRegistered('CommandOrControl+R');
          
          console.log('🧹 [SHORTCUT] Pre-cleanup status:', {
            'CommandOrControl+X': ctrlXRegistered,
            'CommandOrControl+R': ctrlRRegistered
          });
          
          if (ctrlXRegistered) {
            await unregister('CommandOrControl+X');
            console.log('🧹 [SHORTCUT] CommandOrControl+X unregistered');
          }
          
          if (ctrlRRegistered) {
            await unregister('CommandOrControl+R');
            console.log('🧹 [SHORTCUT] CommandOrControl+R unregistered');
          }
          
          console.log('🧹 [SHORTCUT] Global shortcuts cleanup complete');
        } catch (error) {
          console.error('❌ [SHORTCUT] Failed to unregister shortcuts:', error);
        }
      };
      cleanup();
    };
  }, []);
};