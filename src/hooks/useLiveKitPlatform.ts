/**
 * Platform-specific LiveKit Hook
 * Provides unified interface that chooses between Rust (Linux) and JS (Windows/macOS) implementations
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { LiveKitJSService, LiveKitParticipant, ConnectionStatus } from '../services/LiveKitJSService';

export interface LiveKitTokenParams {
  name: string;
  userId: number;
  roomName: string;
  eventType: string;
  metadata?: string;
}

export interface UseLiveKitPlatformReturn {
  // Core methods
  initLiveKitEvents: () => Promise<void>;
  getLiveKitToken: (params: LiveKitTokenParams) => Promise<string>;
  connectToRoom: (token: string, roomName: string) => Promise<ConnectionStatus>;
  disconnectFromRoom: () => Promise<void>;
  
  // Audio control methods
  startAudioPublishing: () => Promise<void>;
  stopAudioPublishing: () => Promise<void>;
  muteRemoteAudio: () => Promise<void>;
  unmuteRemoteAudio: () => Promise<void>;
  
  // Status methods
  getConnectionStatus: () => Promise<ConnectionStatus>;
  getRoomParticipants: () => Promise<LiveKitParticipant[]>;
  getAudioPublishStatus: () => Promise<boolean>;
  
  // Cleanup
  emergencyCleanup: () => Promise<void>;
  
  // State
  platform: string | null;
  isLinux: boolean;
  isInitialized: boolean;
}

export const useLiveKitPlatform = (): UseLiveKitPlatformReturn => {
  const [platform, setPlatform] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const jsServiceRef = useRef<LiveKitJSService | null>(null);

  const isLinux = platform === 'linux';

  // Initialize platform detection
  useEffect(() => {
    const detectPlatform = async () => {
      try {
        if (typeof window !== 'undefined' && (window as any).__TAURI__) {
          const detectedPlatform = await invoke<string>('get_platform');
          setPlatform(detectedPlatform);
          console.log('🖥️ [LIVEKIT_PLATFORM] Detected platform:', detectedPlatform);
          
          // Initialize JS service for non-Linux platforms
          if (detectedPlatform !== 'linux') {
            jsServiceRef.current = new LiveKitJSService();
            console.log('🔧 [LIVEKIT_PLATFORM] Initialized JS service for', detectedPlatform);
          }
          
          setIsInitialized(true);
        } else {
          console.warn('⚠️ [LIVEKIT_PLATFORM] Not running in Tauri environment');
          setIsInitialized(true);
        }
      } catch (error) {
        console.error('❌ [LIVEKIT_PLATFORM] Failed to detect platform:', error);
        setIsInitialized(true);
      }
    };

    detectPlatform();
  }, []);

  // Initialize LiveKit events
  const initLiveKitEvents = useCallback(async (): Promise<void> => {
    if (isLinux) {
      // Use Rust implementation
      await invoke('init_livekit_events');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        await jsServiceRef.current.initEvents();
      }
    }
  }, [isLinux]);

  // Get LiveKit token
  const getLiveKitToken = useCallback(async (params: LiveKitTokenParams): Promise<string> => {
    if (isLinux) {
      // Use Rust implementation
      return await invoke('get_livekit_token', {
        name: params.name,
        userId: params.userId,
        roomName: params.roomName,
        eventType: params.eventType,
        metadata: params.metadata || ''
      });
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        return await jsServiceRef.current.getToken(params);
      }
      throw new Error('JS service not initialized');
    }
  }, [isLinux]);

  // Connect to room
  const connectToRoom = useCallback(async (token: string, roomName: string): Promise<ConnectionStatus> => {
    if (isLinux) {
      // Use Rust implementation
      return await invoke('connect_to_room', { token, roomName });
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        return await jsServiceRef.current.connectToRoom(token, roomName);
      }
      throw new Error('JS service not initialized');
    }
  }, [isLinux]);

  // Disconnect from room
  const disconnectFromRoom = useCallback(async (): Promise<void> => {
    if (isLinux) {
      // Use Rust implementation
      await invoke('disconnect_from_room');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        await jsServiceRef.current.disconnectFromRoom();
      }
    }
  }, [isLinux]);

  // Start audio publishing
  const startAudioPublishing = useCallback(async (): Promise<void> => {
    if (isLinux) {
      // Use Rust implementation
      await invoke('start_audio_publishing');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        await jsServiceRef.current.startAudioPublishing();
      }
    }
  }, [isLinux]);

  // Stop audio publishing
  const stopAudioPublishing = useCallback(async (): Promise<void> => {
    if (isLinux) {
      // Use Rust implementation
      await invoke('stop_audio_publishing');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        await jsServiceRef.current.stopAudioPublishing();
      }
    }
  }, [isLinux]);

  // Mute remote audio
  const muteRemoteAudio = useCallback(async (): Promise<void> => {
    if (isLinux) {
      // Use Rust implementation
      await invoke('mute_remote_audio');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        await jsServiceRef.current.muteRemoteAudio();
      }
    }
  }, [isLinux]);

  // Unmute remote audio
  const unmuteRemoteAudio = useCallback(async (): Promise<void> => {
    if (isLinux) {
      // Use Rust implementation
      await invoke('unmute_remote_audio');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        await jsServiceRef.current.unmuteRemoteAudio();
      }
    }
  }, [isLinux]);

  // Get connection status
  const getConnectionStatus = useCallback(async (): Promise<ConnectionStatus> => {
    if (isLinux) {
      // Use Rust implementation
      return await invoke('get_connection_status');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        return jsServiceRef.current.getConnectionStatus();
      }
      return { connected: false };
    }
  }, [isLinux]);

  // Get room participants
  const getRoomParticipants = useCallback(async (): Promise<LiveKitParticipant[]> => {
    if (isLinux) {
      // Use Rust implementation - convert to expected format
      const rustParticipants = await invoke<any[]>('get_room_participants');
      return rustParticipants.map(p => ({
        identity: p.identity || p.name || 'Unknown',
        name: p.name || p.identity || 'Unknown',
        metadata: p.metadata,
        is_local: p.is_local || false,
        audio_published: p.audio_published || false,
        video_published: p.video_published || false,
      }));
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        return jsServiceRef.current.getRoomParticipants();
      }
      return [];
    }
  }, [isLinux]);

  // Get audio publish status
  const getAudioPublishStatus = useCallback(async (): Promise<boolean> => {
    if (isLinux) {
      // Use Rust implementation
      return await invoke('get_audio_publish_status');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        return jsServiceRef.current.getAudioPublishStatus();
      }
      return false;
    }
  }, [isLinux]);

  // Emergency cleanup
  const emergencyCleanup = useCallback(async (): Promise<void> => {
    if (isLinux) {
      // Use Rust implementation
      await invoke('emergency_livekit_cleanup');
    } else {
      // Use JS implementation
      if (jsServiceRef.current) {
        await jsServiceRef.current.emergencyCleanup();
      }
    }
  }, [isLinux]);

  return {
    // Core methods
    initLiveKitEvents,
    getLiveKitToken,
    connectToRoom,
    disconnectFromRoom,
    
    // Audio control methods
    startAudioPublishing,
    stopAudioPublishing,
    muteRemoteAudio,
    unmuteRemoteAudio,
    
    // Status methods
    getConnectionStatus,
    getRoomParticipants,
    getAudioPublishStatus,
    
    // Cleanup
    emergencyCleanup,
    
    // State
    platform,
    isLinux,
    isInitialized,
  };
};
