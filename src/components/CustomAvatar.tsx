import React, { useState, useEffect } from 'react';

interface CustomAvatarProps {
  src?: string;
  fallbackSrc?: string;
  name?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const CustomAvatar: React.FC<CustomAvatarProps> = ({
  src,
  fallbackSrc,
  name = 'User',
  size = 'md',
  className = ''
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageSrc, setImageSrc] = useState(src);

  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-12 h-12 text-sm',
    lg: 'w-16 h-16 text-base'
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getColorFromName = (name: string) => {
    const colors = [
      'bg-blue-500',
      'bg-green-500', 
      'bg-purple-500',
      'bg-pink-500',
      'bg-yellow-500',
      'bg-indigo-500',
      'bg-red-500',
      'bg-teal-500'
    ];
    
    const index = name.length % colors.length;
    return colors[index];
  };

  useEffect(() => {
    if (src) {
      setImageSrc(src);
      setImageError(false);
    }
  }, [src]);

  const handleImageError = () => {
    console.log('📸 [CUSTOM_AVATAR] Image failed to load:', imageSrc);
    if (fallbackSrc && imageSrc !== fallbackSrc) {
      console.log('📸 [CUSTOM_AVATAR] Trying fallback:', fallbackSrc);
      setImageSrc(fallbackSrc);
    } else {
      console.log('📸 [CUSTOM_AVATAR] Using initials fallback for:', name);
      setImageError(true);
    }
  };

  const handleImageLoad = () => {
    console.log('📸 [CUSTOM_AVATAR] Image loaded successfully:', imageSrc);
    setImageError(false);
  };

  if (imageError || !imageSrc) {
    return (
      <div className={`
        ${sizeClasses[size]} 
        ${getColorFromName(name)} 
        rounded-full 
        flex 
        items-center 
        justify-center 
        text-white 
        font-medium 
        ${className}
      `}>
        {getInitials(name)}
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={name}
      className={`${sizeClasses[size]} rounded-full object-cover ${className}`}
      onError={handleImageError}
      onLoad={handleImageLoad}
      style={{ objectFit: 'cover' }}
    />
  );
};