import React, { useEffect } from "react";
import { <PERSON><PERSON> } from "@heroui/react";
import { TauriIcon as Icon } from "./TauriIcon";
import { useToastHelpers } from "../context/ToastContext";
import { ToastContainer } from "./ui/Toast";

// Import hooks
import { useCallState } from "./call/hooks/useCallState";
import { useCallActions } from "./call/hooks/useCallActions";
import { useCallEvents } from "./call/hooks/useCallEvents";
import { useTeamMembers } from "./call/hooks/useTeamMembers";

// Import components
import { CallLoadingContent } from "./call/components/CallLoadingContent";
import { CallRingingContent } from "./call/components/CallRingingContent";
import { CallConnectedContent } from "./call/components/CallConnectedContent";
import { CallDeclinedContent } from "./call/components/CallDeclinedContent";
import { CallNoResponseContent } from "./call/components/CallNoResponseContent";

export const CallDesktop: React.FC = () => {
  // Get all state from custom hook
  const state = useCallState();
  
  // Get all actions from custom hook
  const actions = useCallActions(state);
  
  // Setup all event listeners
  useCallEvents(state);
  
  // Get team members data
  const { teamMembers, isLoading: isLoadingTeamMembers, error: teamMembersError } = useTeamMembers();
  
  const { showWarning } = useToastHelpers();

  // Reset call duration when call becomes connected
  useEffect(() => {
    if (state.callStatus === "connected") {
      console.log('⏰ [CALL_DESKTOP] Call connected - resetting duration to 0');
      state.setCallDuration(0);
    }
  }, [state.callStatus, state.setCallDuration]);

  // Call duration counter effect
  useEffect(() => {
    let interval: number | null = null;
    
    if (state.callStatus === "connected") {
      console.log('⏰ [CALL_DESKTOP] Starting call duration timer');
      interval = window.setInterval(() => {
        state.setCallDuration(prev => prev + 1);
      }, 1000);
    }
    
    return () => {
      if (interval) {
        console.log('⏰ [CALL_DESKTOP] Clearing call duration timer');
        window.clearInterval(interval);
      }
    };
  }, [state.callStatus, state.setCallDuration]);

  // Start call process when callData is available
  useEffect(() => {
    if (state.callData && !state.isLoading) {
      // Get target employee ID from window or callData
      const targetEmployeeId = (window as any).targetEmployeeId || state.callData.callee.id;
      if (targetEmployeeId) {
        actions.startCallProcess(targetEmployeeId);
      }
    }
  }, [state.callData, state.isLoading]);

  // Helper function to render content based on current state
  const renderContent = () => {
    if (state.error) {
      return (
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="text-red-400 text-sm mb-2">Error loading call</div>
            <div className="text-white text-xs">{state.error}</div>
          </div>
        </div>
      );
    }

    if (state.showDeclinedMessage) {
      return <CallDeclinedContent />;
    }

    if (state.showNoResponseMessage) {
      return <CallNoResponseContent />;
    }

    if (state.isLoading || !state.callData) {
      return (
        <div className="flex-1 h-[318px] border border-[#2A2D3C] border-t-0">
          <CallLoadingContent 
            callData={state.callData}
            onCancel={actions.handleClose}
          />
        </div>
      );
    }

    if (state.callStatus === "ringing") {
      return (
        <div className="flex-1 h-[318px] border border-[#2A2D3C] border-t-0">
          <CallRingingContent 
            callData={state.callData}
            onCancel={actions.handleClose}
          />
        </div>
      );
    }

    if (state.callStatus === "connected") {
      return (
        <div className="flex-1 h-[318px] border border-[#2A2D3C] border-t-0">
          <CallConnectedContent
            callData={state.callData}
            callDuration={state.callDuration}
            participants={state.participants}
            setParticipants={state.setParticipants}
            showAddUser={state.showAddUser}
            setShowAddUser={state.setShowAddUser}
            searchQuery={state.searchQuery}
            setSearchQuery={state.setSearchQuery}
            isMuted={state.isMuted}
            setIsMuted={state.setIsMuted}
            isSpeakerOn={state.isSpeakerOn}
            setIsSpeakerOn={state.setIsSpeakerOn}
            teamMembers={teamMembers}
            meetId={state.meetId}
            onClose={actions.handleClose}
          />
        </div>
      );
    }

    // Fallback - shouldn't normally reach here
    return (
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="text-[#7D8597] text-sm">Unexpected state</div>
          <div className="text-white text-xs">callStatus: {state.callStatus}</div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="h-[350px] w-[400px] bg-gradient-to-br from-[#1A1D2B] to-[#16213E] flex flex-col overflow-hidden">
        {/* SINGLE Top Bar - ثابت برای همه حالت‌ها */}
        <div
          data-tauri-drag-region
          className="flex items-center justify-between h-8 bg-[#1A1D2B] border-b border-[#2A2D3C] px-3 select-none"
        >
          {/* Title */}
          <div className="flex items-center">
            <Icon icon="lucide:phone" className="text-[#1B84FF] text-sm mr-2" />
            <span className="text-white text-xs font-medium">TeamBy - Call</span>
          </div>

          {/* Window Controls */}
          <div className="flex items-center space-x-1">
            <Button
              isIconOnly
              size="sm"
              variant="light"
              className="text-[#7D8597] hover:text-white hover:bg-[#2A2D3C]/50 w-6 h-6 min-w-6"
              onPress={actions.handleMinimize}
            >
              <Icon icon="lucide:minus" className="text-xs" />
            </Button>
            
            <Button
              isIconOnly
              size="sm"
              variant="light"
              className="text-[#7D8597] hover:text-white hover:bg-red-500/20 w-6 h-6 min-w-6"
              onPress={actions.handleClose}
            >
              <Icon icon="lucide:x" className="text-xs" />
            </Button>
          </div>
        </div>

        {/* Dynamic Content */}
        {renderContent()}
      </div>

      {/* Toast Container for notifications */}
      <ToastContainer />
    </>
  );
};