import React, { useState, useEffect } from "react";
import { Input, Avatar, Button, Badge, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/react";
import { TauriIcon as Icon } from "./TauriIcon";
import { UserProfileModal } from "./UserProfileModal";
import type { TeamMember } from "../types/TeamMember";
import { useUser, type Employee, type EmployeeDetail, type UserProfile } from "../context/UserContext";
import { invoke } from "@tauri-apps/api/core";
import type { ScreenViewEmployeeData, ScreenViewWindowData, ScreenViewUserProfile } from "../types/ScreenViewTypes";
import type { CallWindowData } from "../types/CallTypes";
import { useToastHelpers } from "../context/ToastContext";

interface TeamSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export const TeamSidebar: React.FC<TeamSidebarProps> = ({ isOpen, onClose }) => {
  const [selectedUser, setSelectedUser] = useState<TeamMember | null>(null);
  const [profileModalOpen, setProfileModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [employeeDetail, setEmployeeDetail] = useState<EmployeeDetail | null>(null);
  const [isLoadingDetail, setIsLoadingDetail] = useState(false);
  const [screenValidationLoading, setScreenValidationLoading] = useState<Set<number>>(new Set());

  // Get employees data and user profile from UserContext
  const { employees, isLoadingEmployees, employeesError, fetchEmployees } = useUser();

  // Toast helpers for notifications
  const { showError, showSuccess } = useToastHelpers();

  // Debug log
  useEffect(() => {
    console.log('🧑‍🤝‍🧑 [TEAM-SIDEBAR] isOpen:', isOpen);
  }, [isOpen]);



  // Convert Employee to TeamMember for display
  const convertToTeamMember = (employee: Employee): TeamMember => {
    return {
      id: employee.id,
      name: employee.full_name, // Use full_name as name
      fullName: employee.full_name,
      role: employee.position_name || " ", // Use position_name as role
      avatar: employee.avatar,
      isOnline: employee.isonline, // Use isonline
      // Default values for fields not in API
      yearsAtCompany: "N/A",
      slogan: "",
      skills: [],
      projects: [],
      achievements: [],
      email: "",
      phone: "",
      location: "",
    };
  };

  // Convert employees to team members for display
  const teamMembers: TeamMember[] = employees.map(convertToTeamMember);

  // Filter team members based on search query
  const filteredTeamMembers = teamMembers.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleUserClick = async (user: TeamMember) => {
    console.log('👤 [TEAM-SIDEBAR] User clicked:', user.name, 'ID:', user.id);

    // Set initial state and open modal immediately with loading
    setSelectedUser(user);
    setEmployeeDetail(null);
    setIsLoadingDetail(true);
    setProfileModalOpen(true);

    try {
      console.log('🔄 [TEAM-SIDEBAR] Fetching employee detail for ID:', user.id);
      const detail = await invoke<EmployeeDetail>('fetch_employee_detail', { employeeId: user.id });
      console.log('✅ [TEAM-SIDEBAR] Employee detail fetched:', detail.full_name);
      setEmployeeDetail(detail);
    } catch (error) {
      console.error('❌ [TEAM-SIDEBAR] Failed to fetch employee detail:', error);
      // Keep modal open but show basic user info from list
      // The modal will show the selectedUser data as fallback
    } finally {
      setIsLoadingDetail(false);
    }
  };

  // Validate screen sharing permissions via API
  const validateScreenShare = async (employeeId: number): Promise<boolean> => {
    console.log('🔍 [TEAM-SIDEBAR] Validating screen share for employee ID:', employeeId);

    try {
      const response = await invoke<{
        success: boolean;
        message?: string;
        data?: any;
      }>('validate_screen_share', {
        employeeId: employeeId
      });

      console.log('✅ [TEAM-SIDEBAR] Screen share validation response:', response);

      if (response.success) {
        console.log('✅ [TEAM-SIDEBAR] Screen share validation successful');
        return false; //#TODO
      } else {
        console.log('❌ [TEAM-SIDEBAR] Screen share validation failed:', response.message);
        showError('Screen Share Unavailable', response.message || 'Unable to access screen sharing for this employee');
        return false;
      }
    } catch (error) {
      console.error('❌ [TEAM-SIDEBAR] Screen share validation error:', error);

      // Handle specific error types
      const errorMessage = typeof error === 'string' ? error : 'Unknown error';

      if (errorMessage === 'AUTH_EXPIRED') {
        showError('Authentication Error', 'Your session has expired. Please log in again.');
      } else if (errorMessage === 'SCREEN_SHARE_FORBIDDEN') {
        showError('Access Denied', 'You do not have permission to view this employee\'s screen.');
      } else if (errorMessage === 'EMPLOYEE_NOT_FOUND') {
        showError('Employee Not Found', 'The selected employee could not be found.');
      } else if (errorMessage === 'NETWORK_CONNECTION_FAILED') {
        showError('Connection Error', 'Unable to connect to the server. Please check your internet connection.');
      } else {
        showError('Validation Error', errorMessage);
      }

      return false;
    }
  };

  // Handle Call option
  const handleCall = async (member: TeamMember) => {
    console.log('📞 [TEAM-SIDEBAR] ===== CALL FUNCTION CALLED =====');
    console.log('📞 [TEAM-SIDEBAR] Opening Call for:', member.name, 'ID:', member.id);
    console.log('📞 [TEAM-SIDEBAR] Member object:', member);

    try {
      // Get current user profile from Tauri store (cached)
      console.log('📦 [TEAM-SIDEBAR] Getting cached user profile from Tauri store...');
      const cachedProfile = await invoke<UserProfile | null>('get_cached_user_profile');
      console.log('📦 [TEAM-SIDEBAR] Retrieved cached profile:', cachedProfile);

      if (!cachedProfile) {
        console.error('❌ [TEAM-SIDEBAR] Current user profile not available from store');
        showError('Profile Error', 'Current user profile not available');
        return;
      }

      // Find the original employee data from the employees list
      const originalEmployee = employees.find(emp => emp.id === member.id);

      if (!originalEmployee) {
        console.error('❌ [TEAM-SIDEBAR] Employee not found in employees list:', member.id);
        showError('Employee Error', 'Employee not found');
        return;
      }

      // Convert current user profile to TeamMember format for caller  
      const caller: TeamMember = {
        id: cachedProfile.id, // Use user ID from profile (now includes ID)
        name: cachedProfile.full_name,
        fullName: cachedProfile.full_name,
        role: cachedProfile.position || 'Employee',
        avatar: cachedProfile.avatar || '',
        isOnline: true,
        email: cachedProfile.email,
      };



      // Use the member as callee
      const callee: TeamMember = member;

      // Create call window data
      const callData: CallWindowData = {
        caller,
        callee,
        timestamp: Date.now(),
        source: 'team-sidebar',
        callId: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      };

      console.log('📞 [TEAM-SIDEBAR] Call Data:', callData);

      // Call Tauri command to open call window
      await invoke('open_call_window', {
        callData: JSON.stringify(callData)
      });

      console.log('✅ [TEAM-SIDEBAR] Call window opened successfully');
    } catch (error) {
      console.error('❌ [TEAM-SIDEBAR] Failed to open call window:', error);
      showError('Call Error', 'Failed to open call window');
    }
  };

  // Handle Screen View option
  const handleScreenView = async (member: TeamMember) => {
    console.log('🖥️ [TEAM-SIDEBAR] ===== SCREEN VIEW FUNCTION CALLED =====');
    console.log('🖥️ [TEAM-SIDEBAR] Opening Screen View for:', member.name, 'ID:', member.id);
    console.log('🖥️ [TEAM-SIDEBAR] Member object:', member);
    console.log('🖥️ [TEAM-SIDEBAR] Getting user profile from cache...');
    console.log('🖥️ [TEAM-SIDEBAR] Employees list length:', employees.length);

    try {
      // Get current user profile from Tauri store (cached)
      console.log('📦 [TEAM-SIDEBAR] Getting cached user profile from Tauri store...');
      const cachedProfile = await invoke<UserProfile | null>('get_cached_user_profile');
      console.log('📦 [TEAM-SIDEBAR] Retrieved cached profile:', cachedProfile);

      if (!cachedProfile) {
        console.error('❌ [TEAM-SIDEBAR] Current user profile not available from store');
        return;
      }

      const currentUserProfile = cachedProfile;

      // Find the original employee data from the employees list
      const originalEmployee = employees.find(emp => emp.id === member.id);

      if (!originalEmployee) {
        console.error('❌ [TEAM-SIDEBAR] Employee not found in employees list:', member.id);
        return;
      }

      // Convert current user profile to ScreenViewUserProfile
      const currentUser: ScreenViewUserProfile = {
        full_name: currentUserProfile.full_name,
        email: currentUserProfile.email,
        avatar: currentUserProfile.avatar,
        position: currentUserProfile.position,
        company: currentUserProfile.company,
        is_admin: currentUserProfile.is_admin,
        screen_active: currentUserProfile.screen_active,
      };

      // Convert Employee to ScreenViewEmployeeData
      const selectedEmployee: ScreenViewEmployeeData = {
        id: originalEmployee.id, // TODO
        full_name: originalEmployee.full_name,
        position_name: originalEmployee.position_name,
        avatar: originalEmployee.avatar,
        isonline: originalEmployee.isonline,
        screan_active: originalEmployee.screan_active,
        // Add optional fields if available
        email: member.email,
        phone: member.phone,
        location: member.location,
        bio: member.bio,
        skills: member.skills,
        projects: member.projects,
      };

      // Create complete window data
      const windowData: ScreenViewWindowData = {
        currentUser,
        selectedEmployee,
        timestamp: Date.now(),
        source: 'team-sidebar'
      };

      console.log('📊 [TEAM-SIDEBAR] Current User Profile:', currentUser);
      console.log('📊 [TEAM-SIDEBAR] Selected Employee Data:', selectedEmployee);
      console.log('📊 [TEAM-SIDEBAR] Complete Window Data:', windowData);

      // Platform detection for Screen View behavior
      console.log('🔍 [TEAM-SIDEBAR] Detecting platform for Screen View...');

      try {
        // Use Tauri command to get platform information
        const platformInfo = await invoke<string>('get_platform');
        console.log('🖥️ [TEAM-SIDEBAR] Detected platform:', platformInfo);

        if (platformInfo === 'linux') {
          // Linux: Open in default web browser
          console.log('🐧 [TEAM-SIDEBAR] Linux detected - opening Screen View in default browser');

          // Use Tauri command to open in browser
          const encodedData = encodeURIComponent(JSON.stringify(windowData));
          const screenViewUrl = `http://127.0.0.1:3000/screen-view.html?employee_data=${encodedData}`;

          console.log('🔗 [TEAM-SIDEBAR] Browser URL:', screenViewUrl.substring(0, 100) + '...');

          // Open in default browser using Tauri command
          await invoke('open_in_browser', {
            url: screenViewUrl
          });

          console.log('✅ [TEAM-SIDEBAR] Screen View opened in browser successfully');
        } else {
          // Windows/Other OS: Use Tauri window
          console.log('🪟 [TEAM-SIDEBAR] Windows/Other OS detected - opening Screen View in Tauri window');

          // Call Tauri command with complete window data
          await invoke('open_screen_view_window', {
            employeeData: JSON.stringify(windowData)
          });

          console.log('✅ [TEAM-SIDEBAR] Screen View Tauri window opened successfully');
        }
      } catch (error) {
        console.error('❌ [TEAM-SIDEBAR] Platform detection failed, falling back to Tauri window:', error);

        // Fallback: Use Tauri window
        await invoke('open_screen_view_window', {
          employeeData: JSON.stringify(windowData)
        });

        console.log('✅ [TEAM-SIDEBAR] Screen View Tauri window opened successfully (fallback)');
      }
    } catch (error) {
      console.error('❌ [TEAM-SIDEBAR] Failed to open Screen View window:', error);
    }
  };

  // Convert EmployeeDetail to TeamMember for UserProfileModal
  const convertDetailToTeamMember = (detail: EmployeeDetail): TeamMember => {
    // Format working hours: start - end (handle null values)
    const workingHours = detail.typical_working_weekday_start && detail.typical_working_weekday_end
      ? `${detail.typical_working_weekday_start} - ${detail.typical_working_weekday_end}`
      : "Not specified";

    // Format location: country + city (handle null values)
    const location = [detail.country, detail.city].filter(Boolean).join(", ") || "Not specified";

    return {
      id: detail.id,
      name: detail.full_name,
      fullName: detail.full_name,
      role: detail.position || "No Position",
      avatar: selectedUser?.avatar || "", // Use avatar from list since detail doesn't have it
      isOnline: detail.isonline,
      lastSeen: detail.last_seen,
      bio: detail.bio || undefined,
      email: detail.contacts.email,
      phone: detail.contacts.phone,
      location: location,
      yearsAtCompany: workingHours, // Map working hours to yearsAtCompany field
      slogan: detail.slogan || undefined,
      skills: detail.skills,
      projects: detail.projects.map(p => ({
        name: p.name,
        role: p.role,
        color: "primary" // Default color
      })),
      achievements: [], // Not in API
      twitter: detail.contacts.instagram || undefined,
      linkedin: detail.contacts.linkedin || undefined,
      github: detail.contacts.github || undefined,
    };
  };

  // Get the team member to show in modal
  const modalTeamMember = employeeDetail && selectedUser
    ? convertDetailToTeamMember(employeeDetail)
    : selectedUser;

  return (
    <aside
      className={`${
        isOpen ? 'w-[270px]' : 'w-0'
      } transition-all duration-300 ease-in-out overflow-hidden bg-custom-card border-l border-custom-border flex flex-col h-full absolute top-0 right-0 z-10`}
      role="complementary"
      data-testid="team-sidebar"
    >
      {isOpen && (
        <>
          <div className="p-4">
            {/* Search Results Counter */}
            {searchQuery && (
              <div className="mb-2 text-xs text-custom-muted">
                {filteredTeamMembers.length} of {teamMembers.length} employees
              </div>
            )}

            <Input
              classNames={{
                base: "max-w-full",
                inputWrapper: "h-10 bg-custom-sidebar border-custom-border",
              }}
              placeholder="Search employees..."
              startContent={<Icon icon="lucide:search" className="text-custom-muted" />}
              endContent={
                searchQuery && (
                  <Button
                    isIconOnly
                    variant="light"
                    size="sm"
                    className="text-custom-muted hover:text-custom-text min-w-6 w-6 h-6"
                    onPress={() => setSearchQuery("")}
                  >
                    <Icon icon="lucide:x" className="text-sm" />
                  </Button>
                )
              }
              variant="bordered"
              radius="lg"
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
          </div>

          <div className="flex-1 overflow-y-auto custom-scrollbar p-2">
            {/* Loading State */}
            {isLoadingEmployees && (
              <div className="flex items-center justify-center py-8">
                <div className="text-custom-muted">Loading employees...</div>
              </div>
            )}

            {/* Error State */}
            {employeesError && !isLoadingEmployees && (
              <div className="flex flex-col items-center justify-center py-8 px-4">
                <div className="text-red-400 text-sm mb-2">Failed to load employees</div>
                <Button
                  size="sm"
                  variant="bordered"
                  onPress={fetchEmployees}
                  className="text-custom-text border-custom-border"
                >
                  Retry
                </Button>
              </div>
            )}

            {/* No Results Message */}
            {!isLoadingEmployees && !employeesError && searchQuery && filteredTeamMembers.length === 0 && (
              <div className="flex items-center justify-center py-8">
                <div className="text-custom-muted text-sm">No employees found for "{searchQuery}"</div>
              </div>
            )}

            {/* Employees List */}
            {!isLoadingEmployees && !employeesError && filteredTeamMembers.map((member) => {
              // Find the original employee data to access screan_active
              const originalEmployee = employees.find(emp => emp.id === member.id);
              const isScreenSharingDisabled = !originalEmployee?.screan_active;
              const isValidating = screenValidationLoading.has(member.id);

              return (
          <div
            key={member.id}
            className="flex items-center justify-between p-3 hover:bg-custom-border/20 rounded-lg cursor-pointer group"
            onClick={() => handleUserClick(member)}
          >
            <div className="flex items-center">
              <div className="relative">
                <Avatar src={member.avatar} className="h-10 w-10" />
                {member.isOnline && (
                  <div className="absolute bottom-0 right-0">
                  <Badge
                    color="success"
                    shape="circle"
                    placement="bottom-right"
                    size="sm"
                      className="border-2 border-custom-card w-1 h-3"
                    >
                      <span className="sr-only">Online</span>
                    </Badge>
                  </div>
                )}
              </div>
              <div className="ml-3">
                <p className="text-white text-sm font-medium">{member.name}</p>
                <p className="text-custom-muted text-xs">{member.role}</p>
              </div>
            </div>

            <Dropdown>
              <DropdownTrigger>
                <Button
                  isIconOnly
                  variant="light"
                  size="sm"
                  className="text-custom-muted/80 hover:text-custom-text transition-all hover:bg-custom-border/30 min-w-8 w-8 h-8"
                >
                  <Icon icon="lucide:more-vertical" className="text-lg" />
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                aria-label="User actions"
                className="bg-custom-card text-white border border-custom-border"
                onAction={async (key) => {
                  console.log('🔍 [TEAM-SIDEBAR] Dropdown action triggered:', key, 'for member:', member.name);
                  if (key === 'screen') {
                    if (isScreenSharingDisabled) {
                      console.log('⚠️ [TEAM-SIDEBAR] Screen View blocked - screen sharing disabled for:', member.name);
                      return; // Prevent action when screen sharing is disabled
                    }

                    // Add employee to loading state
                    setScreenValidationLoading(prev => new Set(prev).add(member.id));

                    try {
                      console.log('� [TEAM-SIDEBAR] Validating screen share permissions...');
                      const isValid = await validateScreenShare(member.id);

                      if (isValid) {
                        console.log('�🖥️ [TEAM-SIDEBAR] Screen View validation successful, opening screen view...');
                        handleScreenView(member);
                      } else {
                        console.log('❌ [TEAM-SIDEBAR] Screen View validation failed');
                      }
                    } catch (error) {
                      console.error('❌ [TEAM-SIDEBAR] Screen View validation error:', error);
                      showError('Validation Error', 'Failed to validate screen sharing permissions');
                    } finally {
                      // Remove employee from loading state
                      setScreenValidationLoading(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(member.id);
                        return newSet;
                      });
                    }
                  }
                  // Handle other actions here if needed
                }}
              >
                <DropdownItem
                  key="chat"
                  startContent={<Icon icon="lucide:message-circle" className="text-sm" />}
                  className="text-sm"
                >
                  Chat
                </DropdownItem>
                <DropdownItem 
                  key="call" 
                  startContent={<Icon icon="lucide:phone" className="text-sm" />}
                  className="text-sm"
                  onPress={() => handleCall(member)}
                >
                  Call
                </DropdownItem>
                <DropdownItem
                  key="screen"
                  startContent={
                    isValidating ? (
                      <Icon icon="lucide:loader-2" className="text-sm animate-spin opacity-70" />
                    ) : (
                      <Icon icon="lucide:monitor" className={`text-sm ${isScreenSharingDisabled || isValidating ? 'opacity-50' : ''}`} />
                    )
                  }
                  className={`text-sm ${isScreenSharingDisabled || isValidating ? 'opacity-50 cursor-not-allowed' : ''}`}
                  isReadOnly={isScreenSharingDisabled || isValidating}
                  title={
                    isValidating
                      ? "Validating screen share permissions..."
                      : isScreenSharingDisabled
                        ? "Screen View"
                        : "Screen View"
                  }
                >
                  View Screen
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
              );
            })}
          </div>
        </>
      )}

      {/* User Profile Modal */}
      <UserProfileModal
        isOpen={profileModalOpen}
        onClose={() => {
          setProfileModalOpen(false);
          setEmployeeDetail(null);
          setSelectedUser(null);
        }}
        user={modalTeamMember}
        isLoading={isLoadingDetail}
      />
    </aside>
  );
};