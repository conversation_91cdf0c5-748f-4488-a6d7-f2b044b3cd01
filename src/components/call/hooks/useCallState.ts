import { useState, useRef } from "react";
import type { CallWindowData, CallParticipant } from "../../../types/CallTypes";

export type CallStatus = "ringing" | "connected" | "rejected";

export interface CallState {
  // Core call data
  callData: CallWindowData | null;
  setCallData: (data: CallWindowData | null) => void;
  
  // Call status
  callStatus: CallStatus;
  setCallStatus: (status: CallStatus) => void;
  
  // Participants
  participants: CallParticipant[];
  setParticipants: React.Dispatch<React.SetStateAction<CallParticipant[]>>;
  
  // UI states
  isMuted: boolean;
  setIsMuted: (muted: boolean) => void;
  isSpeakerOn: boolean;
  setIsSpeakerOn: (on: boolean) => void;
  showAddUser: boolean;
  setShowAddUser: (show: boolean) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  
  // Loading and error states
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
  
  // Call management states
  callDuration: number;
  setCallDuration: React.Dispatch<React.SetStateAction<number>>;
  callTimeoutId: number | null;
  setCallTimeoutId: (id: number | null) => void;
  callAnswered: boolean;
  setCallAnswered: (answered: boolean) => void;
  meetId: number | null;
  setMeetId: (id: number | null) => void;
  cancelApiSent: boolean;
  setCancelApiSent: (sent: boolean) => void;
  
  // Message states
  showNoResponseMessage: boolean;
  setShowNoResponseMessage: (show: boolean) => void;
  showDeclinedMessage: boolean;
  setShowDeclinedMessage: (show: boolean) => void;
  
  // Window reference
  appWindow: any;
  setAppWindow: (window: any) => void;
  
  // Centrifugo unlisten ref
  centrifugoUnlisten: React.MutableRefObject<(() => void) | null>;
}

export const useCallState = (): CallState => {
  // Core call data
  const [callData, setCallData] = useState<CallWindowData | null>(null);
  const [callStatus, setCallStatus] = useState<CallStatus>("ringing");
  const [participants, setParticipants] = useState<CallParticipant[]>([]);
  
  // UI states
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(true);
  const [showAddUser, setShowAddUser] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  
  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Call management states
  const [callDuration, setCallDuration] = useState(0);
  const [callTimeoutId, setCallTimeoutId] = useState<number | null>(null);
  const [callAnswered, setCallAnswered] = useState(false);
  const [meetId, setMeetId] = useState<number | null>(null);
  const [cancelApiSent, setCancelApiSent] = useState(false);
  
  // Message states
  const [showNoResponseMessage, setShowNoResponseMessage] = useState(false);
  const [showDeclinedMessage, setShowDeclinedMessage] = useState(false);
  
  // Window reference
  const [appWindow, setAppWindow] = useState<any>(null);
  
  // Centrifugo unlisten ref
  const centrifugoUnlisten = useRef<(() => void) | null>(null);

  return {
    // Core call data
    callData,
    setCallData,
    callStatus,
    setCallStatus,
    participants,
    setParticipants,
    
    // UI states
    isMuted,
    setIsMuted,
    isSpeakerOn,
    setIsSpeakerOn,
    showAddUser,
    setShowAddUser,
    searchQuery,
    setSearchQuery,
    
    // Loading and error states
    isLoading,
    setIsLoading,
    error,
    setError,
    
    // Call management states
    callDuration,
    setCallDuration,
    callTimeoutId,
    setCallTimeoutId,
    callAnswered,
    setCallAnswered,
    meetId,
    setMeetId,
    cancelApiSent,
    setCancelApiSent,
    
    // Message states
    showNoResponseMessage,
    setShowNoResponseMessage,
    showDeclinedMessage,
    setShowDeclinedMessage,
    
    // Window reference
    appWindow,
    setAppWindow,
    
    // Centrifugo unlisten ref
    centrifugoUnlisten,
  };
};