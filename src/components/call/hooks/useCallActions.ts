import { useCallback } from "react";
import { getCurrentWindow } from '@tauri-apps/api/window';
import { listen } from '@tauri-apps/api/event';
import { CallService, type CallApiResponse } from "../services/callService";
import type { CallState } from "./useCallState";

export interface CallActions {
  handleClose: () => Promise<void>;
  handleMinimize: () => Promise<void>;
  startCallProcess: (targetEmployeeId: number) => Promise<void>;
  setupCentrifugoListener: () => Promise<void>;
}

export const useCallActions = (state: CallState): CallActions => {
  const {
    callTimeoutId,
    setCallTimeoutId,
    meetId,
    callData,
    cancelApiSent,
    setCancelApiSent,
    appWindow,
    centrifugoUnlisten,
    setMeetId,
    callAnswered,
    setCallAnswered,
    callStatus,
    setCallStatus,
    setParticipants,
    setShowDeclinedMessage,
    setShowNoResponseMessage,
    setError
  } = state;

  // Handle close call
  const handleClose = useCallback(async () => {
    try {
      console.log('📞 [CALL_ACTIONS] Cancel button clicked');
      
      // Clear timeout first
      if (callTimeoutId) {
        window.clearTimeout(callTimeoutId);
        setCallTimeoutId(null);
        console.log('📞 [CALL_ACTIONS] Call timeout cleared');
      }
      
      // If we have meetId and callData, make cancel API call (don't wait for response)
      if (meetId && callData?.callee?.id && !cancelApiSent) {
        console.log('📞 [CALL_ACTIONS] Making cancel API call - meetId:', meetId, 'targetEmployeeId:', callData.callee.id);
        setCancelApiSent(true);
        
        // Fire and forget - don't wait for response
        CallService.cancelCallRequestAsync({
          meetId: meetId,
          targetEmployeeId: callData.callee.id
        });
      } else {
        console.log('📞 [CALL_ACTIONS] No meetId/callData available or cancel API already sent, skipping cancel API call');
      }
      
      // Close window immediately
      await CallService.closeCallWindow();
    } catch (error) {
      console.error('❌ [CALL_ACTIONS] Failed to close call window:', error);
    }
  }, [callTimeoutId, setCallTimeoutId, meetId, callData, cancelApiSent, setCancelApiSent]);

  // Handle minimize window
  const handleMinimize = useCallback(async () => {
    try {
      if (appWindow) {
        await appWindow.minimize();
      }
    } catch (error) {
      console.error('❌ [CALL_ACTIONS] Failed to minimize window:', error);
    }
  }, [appWindow]);

  // Start call process - make API call and setup timeout
  const startCallProcess = useCallback(async (targetEmployeeId: number) => {
    try {
      console.log('📞 [CALL_ACTIONS] Starting call process for employee ID:', targetEmployeeId);
      
      // جلوگیری از اجرای مکرر
      if (callTimeoutId !== null) {
        console.log('📞 [CALL_ACTIONS] Call process already started, skipping...');
        return;
      }
      
      // Make API call
      const response = await CallService.makeCallRequest(targetEmployeeId);
      
      if (response.success && response.status === 201) {
        console.log('✅ [CALL_ACTIONS] Call API successful, setting up 1-minute timeout');
        
        // meet_id logging for debugging and store it
        const apiMeetId = response.data?.meet_id || response.meet_id;
        if (apiMeetId) {
          console.log('📞 [CALL_ACTIONS] Meet ID in response:', apiMeetId);
          setMeetId(apiMeetId);
        } else {
          console.log('⚠️ [CALL_ACTIONS] No meet_id in response:', response);
        }
        
        // Setup 1-minute timeout
        console.log('⏰ [CALL_ACTIONS] ⚡ Setting up 1-minute timeout for call response');
        const timeoutId = window.setTimeout(async () => {
          console.log('⏰ [CALL_ACTIONS] ⚠️ 1-minute timeout reached - checking if call was answered');

          // Check if call was answered
          if (!callAnswered) {
            console.log('📞 [CALL_ACTIONS] ❌ Call not answered, showing no response message');
            setShowNoResponseMessage(true);
            
            // Close window after 3 seconds
            setTimeout(async () => {
              try {
                await CallService.closeCallWindow();
              } catch (error) {
                console.error('❌ [CALL_ACTIONS] Failed to close call window on timeout:', error);
              }
            }, 3000);
          } else {
            console.log('⏰ [CALL_ACTIONS] ✅ Call was already answered - timeout reached but ignored');
          }
        }, 1 * 60 * 1000); // 1 minute
        
        setCallTimeoutId(timeoutId);
        console.log('⏰ [CALL_ACTIONS] ✅ Timeout set successfully with ID:', timeoutId);
        
        // Setup Centrifugo listener for call_accepted message
        await setupCentrifugoListener();
        
      } else {
        console.error('❌ [CALL_ACTIONS] Call API failed:', response);
        setError(response.message || 'Call request failed');
        setTimeout(async () => {
          try {
            await CallService.closeCallWindow();
          } catch (error) {
            console.error('❌ [CALL_ACTIONS] Failed to close call window:', error);
          }
        }, 2000);
      }
    } catch (error) {
      console.error('❌ [CALL_ACTIONS] Failed to start call process:', error);
      setError(error instanceof Error ? error.message : 'Failed to start call');
      setTimeout(async () => {
        try {
          await CallService.closeCallWindow();
        } catch (closeError) {
          console.error('❌ [CALL_ACTIONS] Failed to close call window:', closeError);
        }
      }, 2000);
    }
  }, [callTimeoutId, setMeetId, callAnswered, setShowNoResponseMessage, setCallTimeoutId, setError]);

  // Setup Centrifugo listener for call_accepted and call_declined messages
  const setupCentrifugoListener = useCallback(async () => {
    try {
      console.log('📨 [CALL_ACTIONS] Setting up Centrifugo listener for call messages');
      
      const unlisten = await listen<any>('centrifugo-message', (event) => {
        console.log('📨 [CALL_ACTIONS] Received Centrifugo message:', event.payload);
        
        if (event.payload.type === 'call_accepted') {
          console.log('✅ [CALL_ACTIONS] DISABLED - Call accepted handling moved to useCallEvents.ts');
          // Note: Call accepted handling is now done in useCallEvents.ts to avoid conflicts
          // This listener is kept only for call_declined messages
        } 
        else if (event.payload.type === 'call_declined') {
          console.log('❌ [CALL_ACTIONS] Call declined - closing immediately!');
          
          // Clear timeout
          if (callTimeoutId) {
            window.clearTimeout(callTimeoutId);
            setCallTimeoutId(null);
          }
          
          // Set flag to prevent sending cancel API
          setCancelApiSent(true);
          
          // نمایش پیام رد تماس
          setShowDeclinedMessage(true);
          
          // بعد از 2 ثانیه پنجره را ببند
          setTimeout(async () => {
            try {
              await CallService.closeCallWindow();
            } catch (error) {
              console.error('❌ [CALL_ACTIONS] Failed to close call window after decline:', error);
            }
          }, 2000);
          
          // Stop listening to Centrifugo messages
          if (centrifugoUnlisten.current) {
            centrifugoUnlisten.current();
            centrifugoUnlisten.current = null;
          }
        }
      });
      
      centrifugoUnlisten.current = unlisten;
      
    } catch (error) {
      console.error('❌ [CALL_ACTIONS] Failed to setup Centrifugo listener:', error);
    }
  }, [
    callTimeoutId, 
    setCallTimeoutId, 
    setCallAnswered, 
    setCancelApiSent, 
    setCallStatus, 
    callData, 
    setParticipants, 
    setShowDeclinedMessage,
    centrifugoUnlisten
  ]);

  return {
    handleClose,
    handleMinimize,
    startCallProcess,
    setupCentrifugoListener,
  };
};