import { useState, useEffect, useCallback } from 'react';
import type { TeamMember } from '../../../types/TeamMember';
import { fetchTeamMembers } from '../services/teamMembersService';

interface UseTeamMembersReturn {
  teamMembers: TeamMember[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useTeamMembers = (): UseTeamMembersReturn => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (isLoading && teamMembers.length > 0) return; // Prevent duplicate calls if already loaded

    setIsLoading(true);
    setError(null);

    try {
      const members = await fetchTeamMembers();
      setTeamMembers(members);
      console.log('✅ [useTeamMembers] Team members loaded successfully:', members.length, 'members');
    } catch (err) {
      console.error('❌ [useTeamMembers] Failed to fetch team members:', err);
      const errorMessage = typeof err === 'string' ? err : 'Failed to fetch team members';
      setError(errorMessage);
      
      // On error, keep empty array - no fallback mock data
      setTeamMembers([]);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, teamMembers.length]);

  // Fetch team members on mount
  useEffect(() => {
    fetchData();
  }, []);

  return {
    teamMembers,
    isLoading,
    error,
    refetch: fetchData,
  };
};