import { useEffect, useRef } from "react";
import { getCurrentWindow } from '@tauri-apps/api/window';
import { listen } from '@tauri-apps/api/event';
import { CallService } from "../services/callService";
import type { CallState } from "./useCallState";

export const useCallEvents = (state: CallState) => {
  const {
    callTimeoutId,
    setCallTimeoutId,
    setCancelApiSent,
    setShowDeclinedMessage,
    callStatus,
    meetId,
    callData,
    callAnswered,
    cancelApiSent,
    setIsLoading,
    setAppWindow,
    setCallData,
    setError
  } = state;

  // Use refs to track current values to avoid stale closure issues
  const currentStateRef = useRef({
    callStatus,
    callAnswered,
    cancelApiSent,
    meetId,
    callData
  });

  // Update refs whenever values change
  useEffect(() => {
    currentStateRef.current = {
      callStatus,
      callAnswered,
      cancelApiSent,
      meetId,
      callData
    };
    console.log('📞 [CALL_EVENTS] State updated:', { callStatus, callAnswered, cancelApiSent });
  }, [callStatus, callAnswered, cancelApiSent, meetId, callData]);

  // Direct listener for call_declined messages (independent of MessageRouter)
  useEffect(() => {
    let directUnlisten: (() => void) | null = null;
    
    const setupDirectListener = async () => {
      try {
        directUnlisten = await listen<any>('centrifugo-message', (event) => {
          const payload = event.payload;
          console.log('📞 [CALL_EVENTS] Direct listener received:', payload);
          
          if (payload.type === 'call_accepted') {
            console.log('✅ [CALL_EVENTS] NEW CODE: Direct listener: Call accepted detected - transitioning to connected');
            console.log('🆔 [CALL_EVENTS] NEW CODE: Payload received:', JSON.stringify(payload));
            
            // Set meetId from payload (critical for LiveKit connection)
            if (payload.meet_id) {
              console.log('📞 [CALL_EVENTS] NEW CODE: Setting meetId from payload:', payload.meet_id);
              state.setMeetId(payload.meet_id);
              console.log('📞 [CALL_EVENTS] NEW CODE: MeetId set successfully');
            } else {
              console.warn('⚠️ [CALL_EVENTS] NEW CODE: No meet_id in call_accepted payload');
            }
            
            // Clear timeout to prevent auto-close after 1 minute
            console.log('⏰ [CALL_EVENTS] Current timeout ID:', callTimeoutId);
            if (callTimeoutId) {
              console.log('⏰ [CALL_EVENTS] ✅ Clearing 1-minute timeout because call was accepted');
              window.clearTimeout(callTimeoutId);
              setCallTimeoutId(null);
              console.log('⏰ [CALL_EVENTS] ✅ Timeout successfully cleared - window will not auto-close');
            } else {
              console.log('⏰ [CALL_EVENTS] ⚠️ No timeout found to clear - this might be why cancel API was sent');
            }
            
            // Set flag to prevent sending cancel API
            console.log('📞 [CALL_EVENTS] Setting cancelApiSent = true');
            setCancelApiSent(true);
            
            // Set call answered flag
            console.log('📞 [CALL_EVENTS] Setting callAnswered = true');
            state.setCallAnswered(true);
            
            // Change status to connected
            console.log('📞 [CALL_EVENTS] Setting callStatus = "connected"');
            state.setCallStatus("connected");
            
            // Set participants if callData is available
            if (callData) {
              state.setParticipants([
                { user: callData.caller, status: "connected" },
                { user: callData.callee, status: "connected" }
              ]);
            }
            
            console.log('✅ [CALL_EVENTS] Call state updated to connected successfully');
          }
          else if (payload.type === 'call_declined') {
            console.log('📞 [CALL_EVENTS] Direct listener: Call declined detected - closing window immediately');
            
            // Clear timeout
            if (callTimeoutId) {
              window.clearTimeout(callTimeoutId);
              setCallTimeoutId(null);
            }
            
            // Set flag to prevent sending cancel API
            setCancelApiSent(true);
            
            // نمایش پیام رد تماس
            setShowDeclinedMessage(true);
            
            // بعد از 2 ثانیه پنجره را ببند
            setTimeout(async () => {
              try {
                await CallService.closeCallWindow();
              } catch (error) {
                console.error('❌ [CALL_EVENTS] Failed to close call window after decline:', error);
              }
            }, 2000);
          }
        });
        
        console.log('📞 [CALL_EVENTS] Direct listener setup complete');
      } catch (error) {
        console.error('❌ [CALL_EVENTS] Failed to setup direct listener:', error);
      }
    };
    
    setupDirectListener();
    
    return () => {
      if (directUnlisten) {
        directUnlisten();
        console.log('📞 [CALL_EVENTS] Direct listener cleaned up');
      }
    };
  }, [callTimeoutId, setCallTimeoutId, setCancelApiSent, setShowDeclinedMessage, state, callData]);

  // Handle window close event - send cancel API if call is still ringing
  useEffect(() => {
    const handleWindowClose = () => {
      console.log('📞 [CALL_EVENTS] Window is closing - checking if we need to cancel call');
      
      // Use current values from ref to avoid stale closure issues
      const current = currentStateRef.current;
      console.log('📞 [CALL_EVENTS] Current state on window close:', current);
      
      // Only send cancel API if call is still ringing and we haven't sent it already
      if (current.callStatus === "ringing" && current.meetId && current.callData?.callee?.id && !current.callAnswered && !current.cancelApiSent) {
        console.log('📞 [CALL_EVENTS] Call is still ringing - sending cancel API on window close');
        setCancelApiSent(true);
        
        const requestData = {
          meetId: current.meetId,
          targetEmployeeId: current.callData.callee.id
        };
        
        // Fire and forget - synchronous call for immediate window close
        CallService.cancelCallRequestAsync(requestData);
      } else {
        console.log('📞 [CALL_EVENTS] No need to cancel - call status:', current.callStatus, 'answered:', current.callAnswered, 'already sent:', current.cancelApiSent);
      }
    };

    // Listen for beforeunload event (when user closes window with X button, Alt+F4, etc.)
    const handleBeforeUnload = () => {
      handleWindowClose();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // Also send cancel API when component unmounts if still ringing
      handleWindowClose();
    };
  }, [setCancelApiSent]); // Only include function dependencies, state values are accessed via ref

  // Initialize window and get call data
  useEffect(() => {
    let initiated = false;
    
    const initWindow = async () => {
      if (initiated) {
        console.log('📞 [CALL_EVENTS] initWindow already initiated, skipping...');
        return;
      }
      initiated = true;
      try {
        console.log('📞 [CALL_EVENTS] Initializing call window...');
        setIsLoading(true);

        // Initialize Tauri window
        try {
          const window = getCurrentWindow();
          setAppWindow(window);
        } catch (error) {
          console.error('❌ [CALL_EVENTS] Failed to get current window:', error);
        }

        // Wait for DOM to be fully ready
        await new Promise(resolve => {
          if (document.readyState === 'complete') {
            resolve(null);
          } else {
            window.addEventListener('load', () => resolve(null), { once: true });
          }
        });

        // Initialize Tauri window controls
        try {
          if (typeof window !== 'undefined' && (window as any).__TAURI__) {
            console.log('📞 [CALL_EVENTS] Tauri Available');
            
            // Try multiple times to get callData (sometimes it takes time to inject)
            let windowCallData = null;
            let attempts = 0;
            const maxAttempts = 10;
            
            while (!windowCallData && attempts < maxAttempts) {
              await new Promise(resolve => setTimeout(resolve, 100));
              windowCallData = (window as any).callData;
              attempts++;
              console.log(`📞 [CALL_EVENTS] Attempt ${attempts}: callData =`, windowCallData);
            }
            
            console.log('📞 [CALL_EVENTS] Final callData after', attempts, 'attempts:', windowCallData);
            
            if (windowCallData) {
              // Transform data if needed
              const transformedData: any = {
                caller: {
                  id: windowCallData.caller?.id || 0,
                  name: windowCallData.caller?.name || windowCallData.caller?.fullName || 'Unknown',
                  fullName: windowCallData.caller?.fullName || windowCallData.caller?.name,
                  role: windowCallData.caller?.role || 'Member',
                  avatar: windowCallData.caller?.avatar || windowCallData.caller?.avata || 'https://img.heroui.chat/image/avatar?w=200&h=200&u=1',
                  isOnline: windowCallData.caller?.isOnline || true
                },
                callee: {
                  id: windowCallData.callee?.id || 1,
                  name: windowCallData.callee?.name || windowCallData.callee?.fullName || 'Unknown',
                  fullName: windowCallData.callee?.fullName || windowCallData.callee?.name,
                  role: windowCallData.callee?.role || 'Member',
                  avatar: windowCallData.callee?.avatar || windowCallData.callee?.avata || 'https://img.heroui.chat/image/avatar?w=200&h=200&u=2',
                  isOnline: windowCallData.callee?.isOnline || true
                },
                timestamp: windowCallData.timestamp || Date.now(),
                source: 'team-sidebar' as const
              };
              
              console.log('📞 [CALL_EVENTS] Transformed call data:', transformedData);
              console.log('📞 [CALL_EVENTS] Avatar URLs - Caller:', transformedData.caller.avatar, 'Callee:', transformedData.callee.avatar);
              
              // Set callData first, then wait a tick for state to update
              setCallData(transformedData);
              console.log('📞 [CALL_EVENTS] CallData state updated');
              
              // Wait for next tick to ensure state is updated
              await new Promise(resolve => setTimeout(resolve, 0));
              
              return transformedData.callee.id;
            } else {
              console.error('❌ [CALL_EVENTS] No call data found in window');
              setError('No call data available');
              // Close the window after a short delay to show the error
              setTimeout(async () => {
                try {
                  await CallService.closeCallWindow();
                } catch (error) {
                  console.error('❌ [CALL_EVENTS] Failed to close call window:', error);
                }
              }, 2000);
              return null;
            }
          } else {
            console.error('❌ [CALL_EVENTS] Tauri not available');
            setError('Tauri environment not available');
            // Close the window after a short delay to show the error
            setTimeout(async () => {
              try {
                await CallService.closeCallWindow();
              } catch (error) {
                console.error('❌ [CALL_EVENTS] Failed to close call window:', error);
              }
            }, 2000);
            return null;
          }
        } catch (error) {
          console.error('❌ [CALL_EVENTS] Failed to initialize Tauri:', error);
          setError(error instanceof Error ? error.message : 'Failed to initialize Tauri');
          // Close the window after a short delay to show the error
          setTimeout(async () => {
            try {
              await CallService.closeCallWindow();
            } catch (closeError) {
              console.error('❌ [CALL_EVENTS] Failed to close call window:', closeError);
            }
          }, 2000);
          return null;
        }
      } catch (error) {
        console.error('❌ [CALL_EVENTS] Failed to initialize call window:', error);
        setError(error instanceof Error ? error.message : 'Failed to initialize call window');
        setIsLoading(false);
        // Close the window after a short delay to show the error
        setTimeout(async () => {
          try {
            await CallService.closeCallWindow();
          } catch (closeError) {
            console.error('❌ [CALL_EVENTS] Failed to close call window:', closeError);
          }
        }, 2000);
        return null;
      }
    };

    initWindow().then((targetEmployeeId) => {
      console.log('📞 [CALL_EVENTS] Setting isLoading to false');
      setIsLoading(false);
      
      // Return the target employee ID for parent component to use
      if (targetEmployeeId && typeof window !== 'undefined') {
        (window as any).targetEmployeeId = targetEmployeeId;
      }
    });
  }, [setIsLoading, setAppWindow, setCallData, setError]);

  // Debug callData changes
  useEffect(() => {
    console.log('📞 [CALL_EVENTS] CallData state changed:', {
      callData: !!callData,
      callerAvatar: callData?.caller?.avatar,
      calleeAvatar: callData?.callee?.avatar,
      callerName: callData?.caller?.name,
      calleeName: callData?.callee?.name
    });
  }, [callData]);

  // Debug isLoading changes
  useEffect(() => {
    console.log('📞 [CALL_EVENTS] Loading state changed:', {
      isLoading: state.isLoading,
      callData: !!callData,
      callStatus
    });
  }, [state.isLoading, callData, callStatus]);
};