import React from "react";
import { Button, Input } from "@heroui/react";
import { TauriIcon as Icon } from "../../TauriIcon";
import { CustomAvatar } from "../../CustomAvatar";
import type { CallWindowData, CallParticipant } from "../../../types/CallTypes";
import type { TeamMember } from "../../../types/TeamMember";
import { getFilteredUsers } from "../services/teamMembersService";
import { listen } from "@tauri-apps/api/event";
import { useLiveKitPlatform } from "../../../hooks/useLiveKitPlatform";

interface CallConnectedContentProps {
  callData: CallWindowData | null;
  callDuration: number;
  participants: CallParticipant[];
  setParticipants: React.Dispatch<React.SetStateAction<CallParticipant[]>>;
  showAddUser: boolean;
  setShowAddUser: (show: boolean) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  isMuted: boolean;
  setIsMuted: (muted: boolean) => void;
  isSpeakerOn: boolean;
  setIsSpeakerOn: (on: boolean) => void;
  teamMembers: TeamMember[];
  meetId: number | null;
  onClose: () => Promise<void>;
}

// LiveKit audio control functions and cleanup will be moved inside the component to access the liveKit hook


// Format call duration helper
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
};

export const CallConnectedContent: React.FC<CallConnectedContentProps> = ({
  callData,
  callDuration,
  participants: participantsProp,
  setParticipants,
  showAddUser,
  setShowAddUser,
  searchQuery,
  setSearchQuery,
  isMuted,
  setIsMuted,
  isSpeakerOn,
  setIsSpeakerOn,
  teamMembers,
  meetId,
  onClose
}) => {
  // Local state for LiveKit connection status
  const [localConnectionStatus, setLocalConnectionStatus] = React.useState<'ringing' | 'connected' | 'error'>('ringing');
  // State to track LiveKit initialization status
  const [isLiveKitInitializing, setIsLiveKitInitializing] = React.useState(false);
  const [isLiveKitConnected, setIsLiveKitConnected] = React.useState(false);
  // Local participants state (takes priority over prop)
  const [localParticipants, setLocalParticipants] = React.useState(participantsProp || []);

  // Initialize platform-specific LiveKit service
  const liveKit = useLiveKitPlatform();

  // LiveKit audio control functions
  const handleMuteToggle = async (isMuted: boolean, setIsMuted: (muted: boolean) => void) => {
    try {
      // Check if we're in Tauri environment
      if (typeof window !== 'undefined' && !(window as any).__TAURI__) {
        console.warn('⚠️ [LIVEKIT] Not running in Tauri environment, mute toggle disabled');
        return;
      }

      if (isMuted) {
        // Currently muted, unmute (start audio publishing)
        await liveKit.startAudioPublishing();
        setIsMuted(false);
        console.log('✅ [LIVEKIT] Audio unmuted');
      } else {
        // Currently unmuted, mute (stop audio publishing)
        await liveKit.stopAudioPublishing();
        setIsMuted(true);
        console.log('✅ [LIVEKIT] Audio muted');
      }
    } catch (error) {
      console.error('❌ [LIVEKIT] Failed to toggle mute:', error);
    }
  };

  const handleSpeakerToggle = async (isSpeakerOn: boolean, setIsSpeakerOn: (on: boolean) => void) => {
    try {
      // Check if we're in Tauri environment
      if (typeof window !== 'undefined' && !(window as any).__TAURI__) {
        console.warn('⚠️ [LIVEKIT] Not running in Tauri environment, speaker toggle disabled');
        return;
      }

      if (isSpeakerOn) {
        // Currently speaker is on, turn off (mute remote audio playback)
        await liveKit.muteRemoteAudio();
        setIsSpeakerOn(false);
        console.log('✅ [LIVEKIT] Remote audio muted (speaker off)');
      } else {
        // Currently speaker is off, turn on (unmute remote audio playback)
        await liveKit.unmuteRemoteAudio();
        setIsSpeakerOn(true);
        console.log('✅ [LIVEKIT] Remote audio unmuted (speaker on)');
      }
    } catch (error) {
      console.error('❌ [LIVEKIT] Failed to toggle speaker:', error);
    }
  };

  const handleCallClose = async () => {
    try {
      // Check if we're in Tauri environment
      if (typeof window !== 'undefined' && !(window as any).__TAURI__) {
        console.warn('⚠️ [LIVEKIT] Not running in Tauri environment, skipping LiveKit cleanup');
        await onClose();
        return;
      }

      // Clear participant polling interval
      if ((window as any).livekitParticipantInterval) {
        clearInterval((window as any).livekitParticipantInterval);
        (window as any).livekitParticipantInterval = null;
      }

      // Remove participant change event listener
      if ((window as any).livekitUnlistenParticipantChanges) {
        console.log('🧹 [LIVEKIT] Removing participant change event listener on close');
        (window as any).livekitUnlistenParticipantChanges();
        (window as any).livekitUnlistenParticipantChanges = null;
      }

      // Disconnect from room using platform-specific implementation
      try {
        await liveKit.disconnectFromRoom();
        console.log('✅ [LIVEKIT] Disconnected from room on close');
      } catch (error) {
        console.warn('⚠️ [LIVEKIT] Normal disconnect failed on close, trying emergency cleanup:', error);
        try {
          await liveKit.emergencyCleanup();
          console.log('✅ [LIVEKIT] Emergency cleanup completed on close');
        } catch (emergencyError) {
          console.error('❌ [LIVEKIT] Emergency cleanup also failed on close:', emergencyError);
        }
      }

      // Reset initialization flag and connection status
      (window as any).livekitInitialized = false;
      setLocalConnectionStatus('ringing');
      setIsLiveKitInitializing(false);
      setIsLiveKitConnected(false);
      console.log('🔧 [LIVEKIT] Complete cleanup finished on call close - all states reset');

      // Call original close function
      await onClose();
    } catch (error) {
      console.error('❌ [LIVEKIT] Failed to close call properly:', error);
      // Still try to close the call even if LiveKit cleanup fails
      await onClose();
    }
  };

  // Use local participants instead of prop
  const participants = localParticipants;

  // Sync local participants with parent
  React.useEffect(() => {
    setParticipants(participants);
  }, [participants, setParticipants]);


  // Handle user selection to add to call
  const handleUserSelect = (userId: number) => {
    const selectedUser = teamMembers.find(u => u.id === userId);
    if (selectedUser) {
      // Add user with "ringing" status
      setLocalParticipants([...participants, { user: selectedUser, status: "ringing" }]);
      setShowAddUser(false);
      setSearchQuery("");
      
      // Simulate user accepting or rejecting the call
      setTimeout(() => {
        setLocalParticipants(prev => 
          prev.map(p => 
            p.user.id === userId 
              ? { ...p, status: Math.random() > 0.3 ? "connected" : "rejected" } 
              : p
          )
        );
      }, 4000);
    }
  };

  const filteredUsers = getFilteredUsers(teamMembers, participants, callData, searchQuery);

  // === LiveKit Integration Effect ===
  React.useEffect(() => {
    // Prevent multiple initializations
    if ((window as any).livekitInitialized) {
      console.log('🔧 [LIVEKIT] Already initialized, skipping...');
      return;
    }

    // ابتدا local participant را نمایش بده (قبل از کانکت شدن)
    const initializeLocalParticipant = () => {
      if (!callData?.caller) {
        console.warn('⚠️ [LIVEKIT_REACT] No caller data found, cannot initialize local participant');
        return;
      }
      
      console.log('👤 [LIVEKIT_REACT] Setting initial local participant before LiveKit connection');
      console.log('👤 [LIVEKIT_REACT] Caller data:', callData.caller);
      const localParticipant = {
        user: {
          id: callData.caller.id,
          name: callData.caller.name || 'You',
          avatar: callData.caller.avatar || '',
          role: callData.caller.role || 'You'
        },
        status: 'ringing' as const,
        isLocal: true
      };
      console.log('👤 [LIVEKIT_REACT] Local participant created:', localParticipant);
      setLocalParticipants([localParticipant]);
      console.log('👤 [LIVEKIT_REACT] Local participant set to participants state');
    };

    // Initialize local participant first
    initializeLocalParticipant();

    const initLiveKit = async () => {
      try {
        // Check if we're in Tauri environment
        if (typeof window !== 'undefined' && !(window as any).__TAURI__) {
          console.warn('⚠️ [LIVEKIT] Not running in Tauri environment, LiveKit integration disabled');
          return;
        }

        // Wait for platform detection to complete
        if (!liveKit.isInitialized) {
          console.log('⏳ [LIVEKIT] Waiting for platform detection...');
          return;
        }

        console.log('🔧 [LIVEKIT] Platform detected:', liveKit.platform, '(Linux:', liveKit.isLinux, ')');

        if (!callData || !meetId) {
          console.warn('⚠️ [LIVEKIT] Missing callData or meetId:', { callData: !!callData, meetId });
          return;
        }

        // Mark as initialized to prevent duplicate calls
        (window as any).livekitInitialized = true;
        setIsLiveKitInitializing(true);

        // Initialize LiveKit events using platform-specific implementation
        await liveKit.initLiveKitEvents();

        const caller = callData.caller;
        if (!caller) {
          console.warn('⚠️ [LIVEKIT] Missing caller data');
          return;
        }

        console.log('🔧 [LIVEKIT] Starting LiveKit initialization...');
        
        // Ensure caller.id is a valid number
        if (!caller.id || typeof caller.id !== 'number') {
          console.error('❌ [LIVEKIT] Invalid caller.id:', caller.id);
          throw new Error(`Invalid caller ID: ${caller.id}. Expected a number.`);
        }

        // Step 1: Get LiveKit token using platform-specific implementation
        const metadata = JSON.stringify({
          name: caller.name ?? 'User',
          user_id: caller.id,
          avatar: caller.avatar ?? '',
        });

        const token: string = await liveKit.getLiveKitToken({
          name: caller.name ?? 'User',
          userId: caller.id,
          roomName: String(meetId),
          eventType: 'call',
          metadata: metadata
        });

        console.log('✅ [LIVEKIT] Token received');

        // Step 2: Connect to room using platform-specific implementation
        console.log('🔄 [LIVEKIT] Attempting to connect to room:', String(meetId));
        try {
          await liveKit.connectToRoom(token, String(meetId));
          console.log('✅ [LIVEKIT] Connected to room successfully');
        } catch (connectError) {
          console.error('❌ [LIVEKIT] Failed to connect to room:', connectError);
          throw connectError; // Re-throw to handle in outer catch
        }
        
        // Update connection status
        setLocalConnectionStatus('connected');
        setIsLiveKitConnected(true);
        setIsLiveKitInitializing(false);

        // Note: Microphone permission and audio publishing are handled automatically
        // during connect_to_room() for both Rust and JS implementations
        console.log('🎤 [LIVEKIT] Microphone and audio publishing handled automatically');

        // Step 5: Get initial participants from LiveKit using platform-specific implementation
        console.log('🔄 [LIVEKIT_REACT] Now connected - fetching participants from LiveKit');

        try {
          const initialParticipants = await liveKit.getRoomParticipants();
          if (initialParticipants && Array.isArray(initialParticipants)) {
            const allParticipantsConverted = initialParticipants.map((participant: any) => ({
              user: {
                name: participant.metadata ? JSON.parse(participant.metadata).name || participant.identity : participant.identity,
                id: participant.metadata ? JSON.parse(participant.metadata).user_id || parseInt(participant.identity) || 0 : parseInt(participant.identity) || 0,
                avatar: participant.metadata ? JSON.parse(participant.metadata).avatar || '' : '',
                role: participant.is_local ? (callData?.caller?.role) : ''
              },
              status: 'connected' as const,
              isLocal: participant.is_local
            }));
            console.log('🔄 [LIVEKIT_REACT] Set initial participants from LiveKit:', allParticipantsConverted);
            setLocalParticipants(allParticipantsConverted);
          }
        } catch (error) {
          console.error('❌ [LIVEKIT_REACT] Failed to get initial participants:', error);
        }

        // Step 6: Set up event listener for real-time participant updates (Linux only)
        let unlistenParticipantChanges: (() => void) | null = null;
        if (liveKit.isLinux) {
          console.log('👂 [LIVEKIT_REACT] Setting up Tauri participant change event listener (Linux)');
          unlistenParticipantChanges = await listen('livekit-participant-change', (event) => {
            console.log('📡 [LIVEKIT_REACT] Received participant change event:', event.payload);

            // Immediately refresh participants list when someone joins/leaves
            setTimeout(async () => {
              try {
                const freshParticipants = await liveKit.getRoomParticipants();
                if (freshParticipants && Array.isArray(freshParticipants)) {
                  const allParticipantsConverted = freshParticipants.map((participant: any) => ({
                    user: {
                      name: participant.metadata ? JSON.parse(participant.metadata).name || participant.identity : participant.identity,
                      id: participant.metadata ? JSON.parse(participant.metadata).user_id || parseInt(participant.identity) || 0 : parseInt(participant.identity) || 0,
                      avatar: participant.metadata ? JSON.parse(participant.metadata).avatar || '' : '',
                      role: participant.is_local ? (callData?.caller?.role || 'You') : ''
                    },
                    status: 'connected' as const,
                    isLocal: participant.is_local
                  }));
                  console.log('🔄 [LIVEKIT_REACT] Updated participants from event:', allParticipantsConverted);
                  setLocalParticipants(allParticipantsConverted);
                }
              } catch (error) {
                console.error('❌ [LIVEKIT_REACT] Failed to refresh participants after event:', error);
              }
            }, 100); // Small delay to ensure state is consistent
          });
        } else {
          console.log('👂 [LIVEKIT_REACT] Using JS client events for participant updates (Windows/macOS)');
          // For JS client, events are handled internally by the service
        }

        // Store event listener cleanup function
        (window as any).livekitUnlistenParticipantChanges = unlistenParticipantChanges;

        // Step 7: Start polling for participants (backup method)
        const participantInterval = setInterval(async () => {
          try {
            console.log('🔄 [LIVEKIT_REACT] Polling for room participants...');
            const remoteParticipants = await liveKit.getRoomParticipants();
            console.log('📋 [LIVEKIT_REACT] Received participants from platform service:', remoteParticipants);
            
            if (remoteParticipants && Array.isArray(remoteParticipants)) {
              console.log(`📊 [LIVEKIT_REACT] Processing ${remoteParticipants.length} participants from LiveKit`);
              
              // Convert ALL participants (local + remote) from LiveKit
              const allParticipantsConverted = remoteParticipants.map((participant: any) => {
                console.log(`👤 [LIVEKIT_REACT] Processing participant (${participant.is_local ? 'LOCAL' : 'REMOTE'}):`, participant);
                
                // Try to parse metadata to get user info
                let userData = { 
                  name: participant.identity, 
                  id: parseInt(participant.identity) || 0, 
                  avatar: '',
                  role: participant.is_local ? callData?.caller?.role || 'You' : ''
                };
                
                try {
                  if (participant.metadata) {
                    const metadata = JSON.parse(participant.metadata);
                    userData = {
                      name: metadata.name || participant.identity,
                      id: metadata.user_id || parseInt(participant.identity) || 0,
                      avatar: metadata.avatar || '',
                      role: participant.is_local ? (callData?.caller?.role || 'You') : (metadata.role || '')
                    };
                    console.log(`📝 [LIVEKIT_REACT] Parsed metadata for ${participant.identity}:`, userData);
                  } else {
                    console.log(`⚠️ [LIVEKIT_REACT] No metadata for participant ${participant.identity}, using defaults`);
                  }
                } catch (e) {
                  console.warn('❌ [LIVEKIT_REACT] Failed to parse participant metadata:', e);
                }

                const converted = {
                  user: userData,
                  status: 'connected' as const,
                  isLocal: participant.is_local
                };
                console.log(`✅ [LIVEKIT_REACT] Converted participant:`, converted);
                return converted;
              });

              console.log(`📊 [LIVEKIT_REACT] Setting participants list (${allParticipantsConverted.length} total):`, allParticipantsConverted);
              setLocalParticipants(allParticipantsConverted);
            } else {
              console.warn('⚠️ [LIVEKIT_REACT] Invalid participants data:', remoteParticipants);
            }
          } catch (error) {
            console.error('❌ [LIVEKIT_REACT] Failed to get room participants:', error);
          }
        }, 5000); // Poll every 5 seconds (backup only, primary updates via events)

        // Store interval reference for cleanup
        (window as any).livekitParticipantInterval = participantInterval;

      } catch (error) {
        console.error('❌ [LIVEKIT] Initialization failed:', error);
        setLocalConnectionStatus('error');
        setIsLiveKitInitializing(false);
        setIsLiveKitConnected(false);
      }
    };

    const cleanup = async () => {
      try {
        // Check if we're in Tauri environment
        if (typeof window !== 'undefined' && !(window as any).__TAURI__) {
          console.warn('⚠️ [LIVEKIT] Not running in Tauri environment, skipping LiveKit cleanup');
          return;
        }

        // Clear participant polling interval
        if ((window as any).livekitParticipantInterval) {
          clearInterval((window as any).livekitParticipantInterval);
          (window as any).livekitParticipantInterval = null;
        }

        // Remove participant change event listener
        if ((window as any).livekitUnlistenParticipantChanges) {
          console.log('🧹 [LIVEKIT_REACT] Removing participant change event listener');
          (window as any).livekitUnlistenParticipantChanges();
          (window as any).livekitUnlistenParticipantChanges = null;
        }

        // Disconnect from room using platform-specific implementation
        try {
          await liveKit.disconnectFromRoom();
          console.log('✅ [LIVEKIT] Disconnected from room');
        } catch (error) {
          console.warn('⚠️ [LIVEKIT] Normal disconnect failed, trying emergency cleanup:', error);
          try {
            await liveKit.emergencyCleanup();
            console.log('✅ [LIVEKIT] Emergency cleanup completed');
          } catch (emergencyError) {
            console.error('❌ [LIVEKIT] Emergency cleanup also failed:', emergencyError);
          }
        }

        // Reset initialization flag and connection status
        (window as any).livekitInitialized = false;
        setLocalConnectionStatus('ringing');
        setIsLiveKitInitializing(false);
        setIsLiveKitConnected(false);
        console.log('🔧 [LIVEKIT_REACT] Complete cleanup finished - initialization flag and states reset');
      } catch (error) {
        console.error('❌ [LIVEKIT] Cleanup failed:', error);
      }
    };

    // Add window beforeunload event to ensure cleanup happens even if window is closed
    const handleWindowUnload = () => {
      console.log('🚨 [LIVEKIT_REACT] Window unloading - performing synchronous emergency cleanup');

      // For beforeunload, we need synchronous cleanup
      // Fire and forget async cleanup using platform-specific implementation
      liveKit.emergencyCleanup().catch((error) => {
        console.error('❌ [LIVEKIT_REACT] Emergency cleanup failed on window unload:', error);
      });
      
      // Reset initialization flag immediately
      (window as any).livekitInitialized = false;
      
      // Clear intervals and listeners synchronously
      if ((window as any).livekitParticipantInterval) {
        clearInterval((window as any).livekitParticipantInterval);
        (window as any).livekitParticipantInterval = null;
      }
      if ((window as any).livekitUnlistenParticipantChanges) {
        (window as any).livekitUnlistenParticipantChanges();
        (window as any).livekitUnlistenParticipantChanges = null;
      }
    };

    // Add event listener for window unload
    window.addEventListener('beforeunload', handleWindowUnload);
    
    // Store cleanup function reference for emergency use
    (window as any).livekitCleanup = cleanup;

    initLiveKit();

    // Cleanup on unmount
    return () => {
      // Remove window event listener
      window.removeEventListener('beforeunload', handleWindowUnload);
      
      // Run cleanup
      cleanup();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [callData, meetId, liveKit.isInitialized]); // Re-run when callData, meetId, or platform detection changes

  return (
    <div className="p-4 h-full">
      <div className="flex flex-col h-full">
        <div className="flex justify-between items-center mb-6">
          <h4 className="text-white text-sm font-medium">On Call</h4>
          <span className="text-[#1B84FF] text-sm">{formatTime(callDuration)}</span>
        </div>
        
        {/* Participants - Managed by LiveKit connection status */}
        <div className="mb-4 flex-grow overflow-y-auto">
          {participants.length === 0 ? (
            <div className="flex items-center justify-center py-4">
              <p className="text-[#7D8597] text-xs">
                {isLiveKitInitializing ? "Connecting to room..." : "No participants found"}
              </p>
            </div>
          ) : (
            participants
              .sort((a, b) => (b.isLocal ? 1 : 0) - (a.isLocal ? 1 : 0)) // Local participant first
              .map((participant) => (
                <div key={`${participant.user.id}-${participant.isLocal}`} className="flex items-center mb-2">
                  <CustomAvatar
                    src={participant.user.avatar}
                    fallbackSrc={`https://img.heroui.chat/image/avatar?w=200&h=200&u=${participant.user.id}`}
                    name={participant.user.name || "Participant"}
                    size="sm"
                    className="mr-2"
                  />
                  <div className="flex-1">
                    <p className="text-white text-xs">
                      {participant.user.name}{participant.isLocal ? " (You)" : ""}
                    </p>
                    <p className="text-[#7D8597] text-xs">
                      {participant.status === "ringing" 
                        ? participant.isLocal 
                          ? (isLiveKitInitializing ? "Connecting..." : "Connected")
                          : "Ringing..." 
                        : participant.status === "rejected"
                        ? "Declined"
                        : participant.isLocal && !isLiveKitConnected && isLiveKitInitializing
                        ? "Connecting..."
                        : participant.isLocal && localConnectionStatus === "error"
                        ? "Connection failed"
                        : participant.user.role || "Connected"}
                    </p>
                  </div>
                  <div 
                    className={`w-2 h-2 rounded-full mr-1 
                      ${participant.isLocal
                        ? isLiveKitConnected 
                          ? "bg-green-500" 
                          : isLiveKitInitializing || localConnectionStatus === "ringing"
                          ? "bg-yellow-500 animate-pulse"
                          : localConnectionStatus === "error"
                          ? "bg-red-500"
                          : "bg-yellow-500"
                        : participant.status === "connected" 
                        ? "bg-green-500" 
                        : participant.status === "ringing"
                        ? "bg-yellow-500 animate-pulse"
                        : "bg-red-500"}`}
                  ></div>
                </div>
              ))
          )}
        </div>
        
        {/* Add user section */}
        {showAddUser && (
          <div className="mb-4">
            {searchQuery && filteredUsers.length > 0 && (
              <div className="mb-2 bg-[#12141F] border border-[#2A2D3C] rounded-lg max-h-[120px] overflow-y-auto">
                {filteredUsers.map(user => (
                  <div 
                    key={user.id}
                    className="flex items-center justify-between p-2 hover:bg-[#2A2D3C]/30 cursor-pointer"
                    onClick={() => handleUserSelect(user.id)}
                  >
                    <div className="flex items-center">
                      <CustomAvatar
                        src={user.avatar}
                        fallbackSrc={`https://img.heroui.chat/image/avatar?w=200&h=200&u=${user.id}`}
                        name={user.name || "User"}
                        size="sm"
                        className="mr-2"
                      />
                      <span className="text-white text-xs">{user.name}</span>
                    </div>
                    <Button 
                      isIconOnly 
                      size="sm" 
                      variant="flat" 
                      className="text-[#7D8597]"
                      onPress={() => handleUserSelect(user.id)}
                    >
                      <Icon icon="lucide:plus" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
            <Input
              placeholder="Search users..."
              value={searchQuery}
              onValueChange={setSearchQuery}
              variant="flat"
              size="sm"
              startContent={<Icon icon="lucide:search" className="text-[#7D8597]" />}
              classNames={{
                inputWrapper: "bg-[#12141F] border-[#2A2D3C]"
              }}
            />
          </div>
        )}
        
        {/* Call controls */}
        <div className="flex justify-between items-center">
          <Button
            isIconOnly
            variant="flat"
            size="lg"
            className="text-white"
          >
            <Icon icon="lucide:expand" className="text-lg" />
          </Button>
          
          <Button
            isIconOnly
            variant="flat"
            size="lg"
            className={isMuted ? "bg-[#2A2D3C]/50 text-[#7D8597]" : "text-white"}
            onPress={() => handleMuteToggle(isMuted, setIsMuted)}
          >
            <Icon icon={isMuted ? "lucide:mic-off" : "lucide:mic"} className="text-lg" />
          </Button>
          
          <Button
            isIconOnly
            variant="flat"
            size="lg"
            className={isSpeakerOn ? "text-white" : "bg-[#2A2D3C]/50 text-[#7D8597]"}
            onPress={() => handleSpeakerToggle(isSpeakerOn, setIsSpeakerOn)}
          >
            <Icon icon={isSpeakerOn ? "lucide:volume-2" : "lucide:volume-x"} className="text-lg" />
          </Button>
          
          <Button
            isIconOnly
            variant="flat"
            color={showAddUser ? "primary" : "default"}
            size="lg"
            onPress={() => setShowAddUser(!showAddUser)}
          >
            <Icon icon="lucide:user-plus" className="text-lg" />
          </Button>
          
          <Button
            isIconOnly
            color="danger"
            variant="flat"
            size="lg"
            onPress={() => handleCallClose()}
          >
            <Icon icon="lucide:phone-off" className="text-lg" />
          </Button>
        </div>
      </div>
    </div>
  );
};