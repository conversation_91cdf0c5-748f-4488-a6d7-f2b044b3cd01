import { invoke } from "@tauri-apps/api/core";

export interface CallApiResponse {
  success: boolean;
  status: number;
  data?: any;
  meet_id?: number;
  message?: string;
}

export interface CancelCallRequest {
  meetId: number;
  targetEmployeeId: number;
}

export class CallService {
  /**
   * Make a call request to a target employee
   */
  static async makeCallRequest(targetEmployeeId: number): Promise<CallApiResponse> {
    console.log('📞 [CALL_SERVICE] Making call request to employee ID:', targetEmployeeId);
    
    try {
      const response = await invoke<CallApiResponse>('make_call_request', { 
        targetEmployeeId: targetEmployeeId 
      });
      
      console.log('📞 [CALL_SERVICE] Call API response:', response);
      return response;
    } catch (error) {
      console.error('❌ [CALL_SERVICE] Failed to make call request:', error);
      throw error;
    }
  }

  /**
   * Cancel a call request (caller declines)
   */
  static async cancelCallRequest(data: CancelCallRequest): Promise<CallApiResponse> {
    console.log('📞 [CALL_SERVICE] Canceling call - meetId:', data.meetId, 'targetEmployeeId:', data.targetEmployeeId);
    
    try {
      const response = await invoke<CallApiResponse>('cancel_call_request', {
        meetId: data.meetId,
        targetEmployeeId: data.targetEmployeeId
      });
      
      console.log('📞 [CALL_SERVICE] Cancel API response:', response);
      return response;
    } catch (error) {
      console.error('❌ [CALL_SERVICE] Failed to cancel call:', error);
      throw error;
    }
  }

  /**
   * Cancel call request without waiting for response (fire and forget)
   */
  static cancelCallRequestAsync(data: CancelCallRequest): void {
    this.cancelCallRequest(data).catch(error => {
      console.error('❌ [CALL_SERVICE] Failed to send cancel API:', error);
    });
  }

  /**
   * Close the call window
   */
  static async closeCallWindow(): Promise<void> {
    console.log('📞 [CALL_SERVICE] Closing call window');
    
    try {
      await invoke('close_call_window');
      console.log('✅ [CALL_SERVICE] Call window closed successfully');
    } catch (error) {
      console.error('❌ [CALL_SERVICE] Failed to close call window:', error);
      throw error;
    }
  }
}