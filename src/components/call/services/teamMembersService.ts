import { invoke } from '@tauri-apps/api/core';
import type { TeamMember } from "../../../types/TeamMember";
import type { Employee } from "../../../context/UserContext";

// Convert Employee to TeamMember format
const convertEmployeeToTeamMember = (employee: Employee): TeamMember => {
  return {
    id: employee.id,
    name: employee.full_name,
    fullName: employee.full_name,
    role: employee.position_name || 'Employee',
    avatar: employee.avatar || `https://img.heroui.chat/image/avatar?w=200&h=200&u=${employee.id}`,
    isOnline: employee.isonline,
  };
};

// Fetch team members from API
export const fetchTeamMembers = async (): Promise<TeamMember[]> => {
  try {
    console.log('👥 [CALL] Fetching employees list for call...');
    const employeesData = await invoke<Employee[]>('fetch_employees_list');
    console.log('✅ [CALL] Employees fetched successfully:', employeesData.length, 'employees');
    
    // Convert employees to team members
    const teamMembers = employeesData.map(convertEmployeeToTeamMember);
    
    // Sort by online status (online first)
    teamMembers.sort((a, b) => {
      if (a.isOnline && !b.isOnline) return -1;
      if (!a.isOnline && b.isOnline) return 1;
      return 0;
    });
    
    return teamMembers;
  } catch (err) {
    console.error('❌ [CALL] Failed to fetch employees for call:', err);
    throw err;
  }
};

// Filter team members based on participants and search query
export const getFilteredUsers = (
  members: TeamMember[], 
  participants: any[], 
  callData: any, 
  searchQuery: string
): TeamMember[] => {
  return members.filter(
    member => 
      !participants.some(p => p.user.id === member.id) && 
      callData && member.id !== callData.callee.id &&
      member.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
};