/**
 * LiveKit Token Service for JavaScript Client
 * Handles token requests for Windows/macOS platforms using the same endpoint and payload as Rust implementation
 */

export interface LiveKitTokenRequest {
  event: string;
  live_session_id: string;
  subject: string;
  user_id: number;
  username: string;
  can_publish: string;
  metadata: string;
}

export interface LiveKitTokenParams {
  name: string;
  userId: number;
  roomName: string;
  eventType: string;
  metadata?: string;
}

export class LiveKitTokenService {
  private static readonly TOKEN_ENDPOINT = 'https://habibmeet.nwhco.ir/test/sfu/token';
  
  /**
   * Request LiveKit token from server
   * Matches the Rust implementation's payload structure and endpoint
   */
  static async getToken(params: LiveKitTokenParams): Promise<string> {
    console.log('🔧 [LIVEKIT_TOKEN_JS] Making token request with params:', {
      name: params.name,
      userId: params.userId,
      roomName: params.roomName,
      eventType: params.eventType,
      metadata: params.metadata
    });

    // Ensure userId is a valid number
    if (!params.userId || typeof params.userId !== 'number') {
      throw new Error(`Invalid user ID: ${params.userId}. Expected a number.`);
    }

    // Create username by combining name and userId (matches Rust implementation)
    const username = `${params.name}${params.userId}`;
    
    // Use provided metadata or empty string if None (matches Rust implementation)
    const finalMetadata = params.metadata || '';

    // Prepare request payload (matches Rust LiveKitTokenRequest struct)
    const payload: LiveKitTokenRequest = {
      event: params.eventType,
      live_session_id: params.roomName,
      subject: 'null', // Matches Rust implementation
      user_id: params.userId,
      username,
      can_publish: 'true', // Matches Rust implementation
      metadata: finalMetadata,
    };

    console.log('🌐 [LIVEKIT_TOKEN_JS] Sending request to token server...');

    try {
      const response = await fetch(this.TOKEN_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        const errorMsg = `HTTP ${response.status}: ${errorText}`;
        console.error('❌ [LIVEKIT_TOKEN_JS]', errorMsg);
        throw new Error(errorMsg);
      }

      const responseData = await response.text();
      console.log('✅ [LIVEKIT_TOKEN_JS] Token received successfully');
      
      return responseData;
    } catch (error) {
      const errorMsg = `Failed to get LiveKit token: ${error}`;
      console.error('❌ [LIVEKIT_TOKEN_JS]', errorMsg);
      throw new Error(errorMsg);
    }
  }
}
